"""
Tests for Celery tasks
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import uuid

from app.tasks.microservice_conversion import handle_conversion_request, process_conversion
from app.models.messages import ConversionRequest, ConversionResponse


class TestHandleConversionRequest:
    """Tests for handle_conversion_request task"""
    
    @patch('app.tasks.microservice_conversion.process_conversion')
    def test_handle_valid_request(self, mock_process_conversion, sample_conversion_request_dict):
        """Test handling valid conversion request"""
        # Setup
        mock_task = Mock()
        mock_task.id = "celery-task-123"
        mock_process_conversion.delay.return_value = mock_task
        
        # Execute
        result = handle_conversion_request(sample_conversion_request_dict)
        
        # Verify
        assert result["status"] == "accepted"
        assert result["conversion_id"] == sample_conversion_request_dict["conversion_id"]
        assert result["celery_task_id"] == "celery-task-123"
        
        # Verify task was triggered
        mock_process_conversion.delay.assert_called_once_with(
            conversion_id=sample_conversion_request_dict["conversion_id"],
            input_s3_path=sample_conversion_request_dict["input_s3_path"],
            output_s3_prefix=sample_conversion_request_dict["output_s3_prefix"],
            reply_to_queue=sample_conversion_request_dict["reply_to_queue"]
        )
    
    @patch('app.tasks.microservice_conversion._send_response_to_queue')
    def test_handle_invalid_uuid_request(self, mock_send_response, invalid_conversion_request_dict):
        """Test handling request with invalid UUID"""
        # Execute
        result = handle_conversion_request(invalid_conversion_request_dict)
        
        # Verify
        assert result["status"] == "rejected"
        assert result["conversion_id"] == invalid_conversion_request_dict["conversion_id"]
        assert "conversion_id must be a valid UUID" in result["error"]
        
        # Verify error response was sent to queue
        mock_send_response.assert_called_once()
        response_arg = mock_send_response.call_args[0][0]
        assert isinstance(response_arg, ConversionResponse)
        assert response_arg.status == "FAILED"
    
    def test_handle_missing_field_request(self):
        """Test handling request with missing required field"""
        incomplete_request = {
            "conversion_id": str(uuid.uuid4()),
            "input_s3_path": "s3://test/input.mp4",
            # Missing output_s3_prefix and reply_to_queue
        }
        
        # Execute & Verify
        with pytest.raises(Exception):  # Should raise KeyError or similar
            handle_conversion_request(incomplete_request)
    
    @patch('app.tasks.microservice_conversion.process_conversion')
    def test_handle_request_task_trigger_fails(self, mock_process_conversion, sample_conversion_request_dict):
        """Test handling when task trigger fails"""
        # Setup
        mock_process_conversion.delay.side_effect = Exception("Celery connection failed")
        
        # Execute & Verify
        with pytest.raises(Exception):
            handle_conversion_request(sample_conversion_request_dict)


class TestProcessConversion:
    """Tests for process_conversion task"""
    
    @patch('app.tasks.microservice_conversion.ConversionService')
    @patch('app.tasks.microservice_conversion._send_response_to_queue')
    @patch('app.tasks.microservice_conversion.process_conversion.request')
    def test_process_conversion_success(
        self,
        mock_request,
        mock_send_response,
        mock_conversion_service,
        sample_conversion_id
    ):
        """Test successful conversion processing"""
        # Setup
        mock_request.id = "celery-task-123"
        mock_conversion_service.execute_conversion.return_value = {
            "status": "completed",
            "conversion_id": sample_conversion_id,
            "task_id": "celery-task-123",
            "output_s3_url": "s3://test/output/manifest.mpd",
            "processing_duration_seconds": 120.5
        }

        # Execute
        result = process_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            reply_to_queue="test.queue"
        )

        # Verify
        assert result["status"] == "completed"
        assert result["conversion_id"] == sample_conversion_id
        assert result["processing_duration_seconds"] == 120.5

        # Verify service was called
        mock_conversion_service.execute_conversion.assert_called_once()

        # Verify response was sent to queue
        mock_send_response.assert_called_once()
        response_arg = mock_send_response.call_args[0][0]
        assert isinstance(response_arg, ConversionResponse)
        assert response_arg.status == "COMPLETED"
    
    @patch('app.tasks.microservice_conversion.ConversionService')
    @patch('app.tasks.microservice_conversion._send_response_to_queue')
    def test_process_conversion_duplicate(
        self, 
        mock_send_response, 
        mock_conversion_service, 
        sample_conversion_id,
        mock_celery_task
    ):
        """Test processing duplicate conversion"""
        # Setup
        mock_conversion_service.execute_conversion.return_value = {
            "status": "duplicate",
            "conversion_id": sample_conversion_id,
            "task_id": mock_celery_task.request.id,
            "error": "Conversion already exists with status: COMPLETED",
            "existing_status": "COMPLETED",
            "processing_duration_seconds": 0
        }
        
        # Execute
        with patch.object(process_conversion, 'request', mock_celery_task.request):
            result = process_conversion(
                conversion_id=sample_conversion_id,
                input_s3_path="s3://test/input.mp4",
                output_s3_prefix="s3://test/output",
                reply_to_queue="test.queue"
            )
        
        # Verify
        assert result["status"] == "duplicate"
        assert result["conversion_id"] == sample_conversion_id
        assert result["existing_status"] == "COMPLETED"
        
        # Verify no response was sent for duplicate (already handled)
        mock_send_response.assert_not_called()
    
    @patch('app.tasks.microservice_conversion.ConversionService')
    @patch('app.tasks.microservice_conversion._send_response_to_queue')
    def test_process_conversion_failed(
        self, 
        mock_send_response, 
        mock_conversion_service, 
        sample_conversion_id,
        mock_celery_task
    ):
        """Test failed conversion processing"""
        # Setup
        mock_conversion_service.execute_conversion.return_value = {
            "status": "failed",
            "conversion_id": sample_conversion_id,
            "task_id": mock_celery_task.request.id,
            "error": "FFmpeg conversion failed",
            "processing_duration_seconds": 45.2
        }
        
        # Execute
        with patch.object(process_conversion, 'request', mock_celery_task.request):
            result = process_conversion(
                conversion_id=sample_conversion_id,
                input_s3_path="s3://test/input.mp4",
                output_s3_prefix="s3://test/output",
                reply_to_queue="test.queue"
            )
        
        # Verify
        assert result["status"] == "failed"
        assert result["conversion_id"] == sample_conversion_id
        assert "FFmpeg conversion failed" in result["error"]
        
        # Verify error response was sent to queue
        mock_send_response.assert_called_once()
        response_arg = mock_send_response.call_args[0][0]
        assert isinstance(response_arg, ConversionResponse)
        assert response_arg.status == "failed"
    
    @patch('app.tasks.microservice_conversion.ConversionService')
    @patch('app.tasks.microservice_conversion._send_response_to_queue')
    def test_process_conversion_service_exception(
        self, 
        mock_send_response, 
        mock_conversion_service, 
        sample_conversion_id,
        mock_celery_task
    ):
        """Test processing when service raises exception"""
        # Setup
        mock_conversion_service.execute_conversion.side_effect = Exception("Unexpected error")
        
        # Execute
        with patch.object(process_conversion, 'request', mock_celery_task.request):
            result = process_conversion(
                conversion_id=sample_conversion_id,
                input_s3_path="s3://test/input.mp4",
                output_s3_prefix="s3://test/output",
                reply_to_queue="test.queue"
            )
        
        # Verify
        assert result["status"] == "failed"
        assert result["conversion_id"] == sample_conversion_id
        assert "Unexpected error" in result["error"]
        
        # Verify error response was sent to queue
        mock_send_response.assert_called_once()
        response_arg = mock_send_response.call_args[0][0]
        assert isinstance(response_arg, ConversionResponse)
        assert response_arg.status == "failed"
    
    @patch('app.tasks.microservice_conversion.ConversionService')
    def test_process_conversion_with_custom_bitrates(
        self, 
        mock_conversion_service, 
        sample_conversion_id,
        mock_celery_task
    ):
        """Test processing with custom bitrates"""
        # Setup
        custom_bitrates = [300, 600, 1200]
        mock_conversion_service.execute_conversion.return_value = {
            "status": "completed",
            "conversion_id": sample_conversion_id,
            "task_id": mock_celery_task.request.id,
            "output_s3_url": "s3://test/output/manifest.mpd",
            "processing_duration_seconds": 120.5
        }
        
        # Execute
        with patch.object(process_conversion, 'request', mock_celery_task.request):
            process_conversion(
                conversion_id=sample_conversion_id,
                input_s3_path="s3://test/input.mp4",
                output_s3_prefix="s3://test/output",
                reply_to_queue="test.queue",
                bitrates=custom_bitrates
            )
        
        # Verify service was called with custom bitrates
        call_args = mock_conversion_service.execute_conversion.call_args
        assert call_args[1]["bitrates"] == custom_bitrates
