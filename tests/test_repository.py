"""
Tests for MediaConversionRepository
"""

import pytest
import uuid
from unittest.mock import Mock, patch
from sqlalchemy.exc import SQLAlchemyError
from psycopg2.errors import UniqueViolation

from app.repositories.media_conversion_repository import MediaConversionRepository
from app.database.models import MediaConversion, MediaType, OutputFormat


class TestMediaConversionRepository:
    """Tests for MediaConversionRepository"""
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_create_conversion_success(self, mock_get_session, sample_conversion_id):
        """Test successful conversion creation"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Execute
        result = MediaConversionRepository.create_conversion(
            conversion_id=sample_conversion_id,
            input_path="s3://test/input.mp4",
            media_type=MediaType.VIDEO,
            output_format=OutputFormat.DASH
        )
        
        # Verify
        assert result is not None
        assert result.id == sample_conversion_id
        assert result.input_path == "s3://test/input.mp4"
        assert result.status == "PENDING"
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_create_conversion_duplicate_returns_none(self, mock_get_session, sample_conversion_id):
        """Test that duplicate conversion returns None"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Simulate duplicate key error
        duplicate_error = SQLAlchemyError("duplicate key value violates unique constraint")
        mock_session.commit.side_effect = duplicate_error
        
        # Execute
        result = MediaConversionRepository.create_conversion(
            conversion_id=sample_conversion_id,
            input_path="s3://test/input.mp4",
            media_type=MediaType.VIDEO,
            output_format=OutputFormat.DASH
        )
        
        # Verify
        assert result is None
        mock_session.rollback.assert_called_once()
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_create_conversion_other_error_returns_none(self, mock_get_session, sample_conversion_id):
        """Test that other database errors return None"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Simulate other database error
        other_error = SQLAlchemyError("connection timeout")
        mock_session.commit.side_effect = other_error
        
        # Execute
        result = MediaConversionRepository.create_conversion(
            conversion_id=sample_conversion_id,
            input_path="s3://test/input.mp4",
            media_type=MediaType.VIDEO,
            output_format=OutputFormat.DASH
        )
        
        # Verify
        assert result is None
        mock_session.rollback.assert_called_once()
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_get_conversion_exists(self, mock_get_session, sample_conversion_id, sample_media_conversion):
        """Test getting existing conversion"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = sample_media_conversion
        
        # Execute
        result = MediaConversionRepository.get_conversion(sample_conversion_id)
        
        # Verify
        assert result == sample_media_conversion
        mock_session.query.assert_called_once_with(MediaConversion)
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_get_conversion_not_exists(self, mock_get_session, sample_conversion_id):
        """Test getting non-existing conversion"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # Execute
        result = MediaConversionRepository.get_conversion(sample_conversion_id)
        
        # Verify
        assert result is None
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_update_status_success(self, mock_get_session, sample_conversion_id):
        """Test successful status update"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_conversion = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_conversion
        
        metadata = {"test": "data"}
        
        # Execute
        result = MediaConversionRepository.update_status(
            conversion_id=sample_conversion_id,
            status="PROCESSING",
            metadata=metadata
        )
        
        # Verify
        assert result is True
        assert mock_conversion.status == "PROCESSING"
        assert mock_conversion.metadata_json == '{"test": "data"}'
        mock_session.commit.assert_called_once()
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_update_status_conversion_not_found(self, mock_get_session, sample_conversion_id):
        """Test status update when conversion not found"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # Execute
        result = MediaConversionRepository.update_status(
            conversion_id=sample_conversion_id,
            status="PROCESSING"
        )
        
        # Verify
        assert result is False
        mock_session.commit.assert_not_called()
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_update_status_database_error(self, mock_get_session, sample_conversion_id):
        """Test status update with database error"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_conversion = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_conversion
        
        # Simulate database error
        mock_session.commit.side_effect = SQLAlchemyError("connection error")
        
        # Execute
        result = MediaConversionRepository.update_status(
            conversion_id=sample_conversion_id,
            status="PROCESSING"
        )
        
        # Verify
        assert result is False
        mock_session.rollback.assert_called_once()
    
    @patch('app.repositories.media_conversion_repository.get_db_session')
    def test_update_status_with_output_path(self, mock_get_session, sample_conversion_id):
        """Test status update with output path"""
        # Setup
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        mock_conversion = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_conversion

        metadata = {"output_path": "s3://test/output/manifest.mpd", "duration": 120.5}

        # Execute
        result = MediaConversionRepository.update_status(
            conversion_id=sample_conversion_id,
            status="COMPLETED",
            metadata=metadata
        )

        # Verify
        assert result is True
        assert mock_conversion.status == "COMPLETED"
        mock_session.commit.assert_called_once()
