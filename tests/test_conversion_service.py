"""
Tests for ConversionService
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os

from app.services.conversion_service import ConversionService
from app.database.models import MediaConversion


class TestConversionService:
    """Tests for ConversionService"""
    
    @patch('app.services.conversion_service.MediaConversionRepository')
    @patch('app.services.conversion_service.S3Client')
    @patch('app.services.conversion_service.get_converter')
    @patch('tempfile.mkdtemp')
    def test_execute_conversion_success(
        self, 
        mock_mkdtemp, 
        mock_get_converter, 
        mock_s3_client, 
        mock_repo,
        sample_conversion_id
    ):
        """Test successful conversion execution"""
        # Setup
        temp_dir = "/tmp/test_conversion"
        mock_mkdtemp.return_value = temp_dir
        
        mock_conversion = Mock()
        mock_conversion.id = sample_conversion_id
        mock_repo.create_conversion.return_value = mock_conversion
        mock_repo.update_status.return_value = True
        mock_repo.update_completion.return_value = True
        
        mock_s3_instance = Mock()
        mock_s3_client.return_value = mock_s3_instance
        mock_s3_instance.download_file.return_value = True
        mock_s3_instance.upload_directory.return_value = ["file1.mp4", "manifest.mpd"]
        
        mock_converter = Mock()
        mock_get_converter.return_value = mock_converter
        mock_converter.convert.return_value = {
            "status": "success",
            "output_files": ["manifest.mpd", "segment1.mp4"],
            "metadata": {"duration": 120}
        }
        
        # Execute
        result = ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )
        
        # Verify
        assert result["status"] == "completed"
        assert result["conversion_id"] == sample_conversion_id
        assert "processing_duration_seconds" in result
        
        # Verify repository calls
        mock_repo.create_conversion.assert_called_once()
        mock_repo.update_status.assert_called()
        mock_repo.update_completion.assert_called_once()
        
        # Verify S3 operations
        mock_s3_instance.download_file.assert_called_once()
        mock_s3_instance.upload_directory.assert_called_once()
        
        # Verify converter call
        mock_converter.convert.assert_called_once()
    
    @patch('app.services.conversion_service.MediaConversionRepository')
    def test_execute_conversion_duplicate(self, mock_repo, sample_conversion_id):
        """Test conversion execution with duplicate ID"""
        # Setup - create_conversion returns None (duplicate)
        mock_repo.create_conversion.return_value = None
        
        # Setup existing conversion
        mock_existing = Mock()
        mock_existing.status = "COMPLETED"
        mock_repo.get_conversion.return_value = mock_existing
        
        # Execute
        result = ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )
        
        # Verify
        assert result["status"] == "duplicate"
        assert result["conversion_id"] == sample_conversion_id
        assert result["existing_status"] == "COMPLETED"
        assert result["processing_duration_seconds"] == 0
        
        # Verify no processing occurred
        mock_repo.update_status.assert_not_called()
        mock_repo.update_completion.assert_not_called()
    
    @patch('app.services.conversion_service.MediaConversionRepository')
    def test_execute_conversion_create_failed(self, mock_repo, sample_conversion_id):
        """Test conversion execution when creation fails"""
        # Setup - create_conversion returns None and no existing conversion
        mock_repo.create_conversion.return_value = None
        mock_repo.get_conversion.return_value = None
        
        # Execute
        result = ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )
        
        # Verify
        assert result["status"] == "failed"
        assert result["conversion_id"] == sample_conversion_id
        assert "Failed to create conversion record" in result["error"]
    
    @patch('app.services.conversion_service.MediaConversionRepository')
    @patch('app.services.conversion_service.S3Client')
    @patch('tempfile.mkdtemp')
    def test_execute_conversion_download_failed(
        self, 
        mock_mkdtemp, 
        mock_s3_client, 
        mock_repo,
        sample_conversion_id
    ):
        """Test conversion execution when S3 download fails"""
        # Setup
        temp_dir = "/tmp/test_conversion"
        mock_mkdtemp.return_value = temp_dir
        
        mock_conversion = Mock()
        mock_conversion.id = sample_conversion_id
        mock_repo.create_conversion.return_value = mock_conversion
        mock_repo.update_status.return_value = True
        mock_repo.update_completion.return_value = True
        
        mock_s3_instance = Mock()
        mock_s3_client.return_value = mock_s3_instance
        mock_s3_instance.download_file.side_effect = Exception("S3 download failed")
        
        # Execute
        result = ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )
        
        # Verify
        assert result["status"] == "failed"
        assert result["conversion_id"] == sample_conversion_id
        assert "S3 download failed" in result["error"]
        
        # Verify failure was recorded
        mock_repo.update_completion.assert_called_once()
        call_args = mock_repo.update_completion.call_args
        assert call_args[1]["status"] == "FAILED"
    
    @patch('app.services.conversion_service.MediaConversionRepository')
    @patch('app.services.conversion_service.S3Client')
    @patch('app.services.conversion_service.get_converter')
    @patch('tempfile.mkdtemp')
    def test_execute_conversion_converter_failed(
        self, 
        mock_mkdtemp, 
        mock_get_converter, 
        mock_s3_client, 
        mock_repo,
        sample_conversion_id
    ):
        """Test conversion execution when converter fails"""
        # Setup
        temp_dir = "/tmp/test_conversion"
        mock_mkdtemp.return_value = temp_dir
        
        mock_conversion = Mock()
        mock_conversion.id = sample_conversion_id
        mock_repo.create_conversion.return_value = mock_conversion
        mock_repo.update_status.return_value = True
        mock_repo.update_completion.return_value = True
        
        mock_s3_instance = Mock()
        mock_s3_client.return_value = mock_s3_instance
        mock_s3_instance.download_file.return_value = True
        
        mock_converter = Mock()
        mock_get_converter.return_value = mock_converter
        mock_converter.convert.return_value = {
            "status": "error",
            "error": "FFmpeg conversion failed"
        }
        
        # Execute
        result = ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )
        
        # Verify
        assert result["status"] == "failed"
        assert result["conversion_id"] == sample_conversion_id
        assert "FFmpeg conversion failed" in result["error"]
    
    @patch('app.services.conversion_service.MediaConversionRepository')
    @patch('app.services.conversion_service.S3Client')
    @patch('app.services.conversion_service.get_converter')
    @patch('tempfile.mkdtemp')
    @patch('shutil.rmtree')
    def test_execute_conversion_cleanup_temp_directory(
        self, 
        mock_rmtree,
        mock_mkdtemp, 
        mock_get_converter, 
        mock_s3_client, 
        mock_repo,
        sample_conversion_id
    ):
        """Test that temporary directory is cleaned up"""
        # Setup
        temp_dir = "/tmp/test_conversion"
        mock_mkdtemp.return_value = temp_dir
        
        mock_conversion = Mock()
        mock_repo.create_conversion.return_value = mock_conversion
        mock_repo.update_status.return_value = True
        mock_repo.update_completion.return_value = True
        
        mock_s3_instance = Mock()
        mock_s3_client.return_value = mock_s3_instance
        mock_s3_instance.download_file.return_value = True
        mock_s3_instance.upload_directory.return_value = ["file1.mp4"]
        
        mock_converter = Mock()
        mock_get_converter.return_value = mock_converter
        mock_converter.convert.return_value = {
            "status": "success",
            "output_files": ["manifest.mpd"]
        }
        
        # Execute
        ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )
        
        # Verify cleanup
        mock_rmtree.assert_called_once_with(temp_dir, ignore_errors=True)

    @patch('app.services.conversion_service.MediaConversionRepository')
    @patch('app.services.conversion_service.S3Client')
    @patch('app.services.conversion_service.get_converter')
    @patch('tempfile.mkdtemp')
    def test_execute_conversion_upload_failed(
        self,
        mock_mkdtemp,
        mock_get_converter,
        mock_s3_client,
        mock_repo,
        sample_conversion_id
    ):
        """Test conversion execution when S3 upload fails"""
        # Setup
        temp_dir = "/tmp/test_conversion"
        mock_mkdtemp.return_value = temp_dir

        mock_conversion = Mock()
        mock_conversion.id = sample_conversion_id
        mock_repo.create_conversion.return_value = mock_conversion
        mock_repo.update_status.return_value = True
        mock_repo.update_completion.return_value = True

        mock_s3_instance = Mock()
        mock_s3_client.return_value = mock_s3_instance
        mock_s3_instance.download_file.return_value = True
        mock_s3_instance.upload_directory.side_effect = Exception("S3 upload failed")

        mock_converter = Mock()
        mock_get_converter.return_value = mock_converter
        mock_converter.convert.return_value = {
            "status": "success",
            "output_files": ["manifest.mpd", "segment1.mp4"]
        }

        # Execute
        result = ConversionService.execute_conversion(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            celery_task_id="task-123",
            reply_to_queue="test.queue"
        )

        # Verify
        assert result["status"] == "failed"
        assert result["conversion_id"] == sample_conversion_id
        assert "S3 upload failed" in result["error"]
