# 🎭 Poetry Setup Guide

Este guia explica como usar Poetry no projeto Media Convert Service.

## 📋 O que é Poetry?

Poetry é uma ferramenta moderna para gerenciamento de dependências e empacotamento em Python que oferece:

- **Resolução de dependências determinística** - Garan<PERSON> builds reproduzíveis
- **Ambiente virtual automático** - Cria e gerencia ambientes virtuais automaticamente
- **Arquivo de lock** - `poetry.lock` garante versões exatas das dependências
- **Comandos simples** - Interface intuitiva para gerenciar dependências
- **Compatibilidade com pip** - Funciona com PyPI e repositórios privados

## 🚀 Instalação

### 1. Instalar Poetry

```bash
# Instalação oficial (recomendada)
curl -sSL https://install.python-poetry.org | python3 -

# Adicionar ao PATH (adicione ao seu ~/.bashrc ou ~/.zshrc)
export PATH="$HOME/.local/bin:$PATH"

# Verificar instalação
poetry --version
```

### 2. Configurar o projeto

```bash
# Instalar dependências do projeto
poetry install

# Ativar ambiente virtual
poetry shell
```

## 🛠️ Comandos Básicos

### Gerenciamento de Dependências

```bash
# Instalar todas as dependências
poetry install

# Instalar apenas dependências de produção
poetry install --only=main

# Atualizar dependências
poetry update

# Adicionar nova dependência
poetry add requests

# Adicionar dependência de desenvolvimento
poetry add --group dev pytest

# Remover dependência
poetry remove requests

# Mostrar dependências instaladas
poetry show

# Mostrar árvore de dependências
poetry show --tree
```

### Ambiente Virtual

```bash
# Ativar ambiente virtual
poetry shell

# Executar comando no ambiente virtual
poetry run python script.py
poetry run pytest
poetry run celery worker

# Mostrar informações do ambiente
poetry env info

# Listar ambientes virtuais
poetry env list

# Remover ambiente virtual
poetry env remove python
```

## 🐳 Docker com Poetry

### Usando o serviço Poetry no Docker Compose

```bash
# Construir imagem com Poetry
make build-poetry

# Executar worker com Poetry
make worker-poetry

# Logs do worker Poetry
docker-compose logs -f media-convert-worker-poetry
```

### Dockerfile Poetry

O projeto inclui `docker/Dockerfile.poetry` que:

- Instala Poetry na imagem
- Configura Poetry para usar venv no projeto
- Instala dependências usando `poetry install`
- Executa aplicação com `poetry run`

## 📁 Estrutura de Arquivos

```
├── pyproject.toml      # Configuração do Poetry e dependências
├── poetry.lock         # Versões exatas das dependências (não editar)
├── requirements.txt    # Mantido para compatibilidade
├── requirements-dev.txt # Mantido para compatibilidade
└── docker/
    ├── Dockerfile.poetry    # Dockerfile usando Poetry
    └── entrypoint-poetry.sh # Entrypoint para Poetry
```

## ⚙️ Configuração do pyproject.toml

```toml
[tool.poetry]
name = "media-convert"
version = "1.0.0"
description = "Media Convert Service"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
celery = "5.3.4"
# ... outras dependências

[tool.poetry.group.dev.dependencies]
pytest = "7.4.4"
ruff = "0.1.6"
# ... dependências de desenvolvimento
```

## 🔄 Migração de requirements.txt

O projeto mantém compatibilidade com ambos os sistemas:

### Opção 1: Usar Poetry (Recomendado)
```bash
# Instalar com Poetry
poetry install

# Executar worker com Poetry
make worker-poetry
```

### Opção 2: Usar requirements.txt (Legado)
```bash
# Instalar com pip
pip install -r requirements-dev.txt

# Executar worker tradicional
make worker
```

## 🎯 Comandos Make com Poetry

```bash
# Poetry específicos
make poetry-install      # poetry install
make poetry-update       # poetry update
make poetry-shell        # poetry shell
make poetry-add PACKAGE=nome-do-pacote
make poetry-add-dev PACKAGE=nome-do-pacote

# Docker com Poetry
make build-poetry        # Construir imagem Poetry
make worker-poetry       # Executar worker Poetry
```

## 🔧 Troubleshooting

### Problema: "Current Python version not allowed"
```bash
# Verificar versão Python no projeto
poetry env info

# Usar Python específico
poetry env use python3.11
```

### Problema: Dependências conflitantes
```bash
# Limpar cache
poetry cache clear pypi --all

# Reinstalar dependências
poetry install --sync
```

### Problema: Ambiente virtual corrompido
```bash
# Remover ambiente atual
poetry env remove python

# Reinstalar
poetry install
```

## 📚 Recursos Adicionais

- [Documentação oficial do Poetry](https://python-poetry.org/docs/)
- [Guia de migração](https://python-poetry.org/docs/managing-dependencies/)
- [Configuração avançada](https://python-poetry.org/docs/configuration/)

## 🎉 Vantagens do Poetry

1. **Resolução determinística** - Builds reproduzíveis
2. **Gerenciamento automático de venv** - Sem configuração manual
3. **Separação clara** - Dependências de produção vs desenvolvimento
4. **Comandos intuitivos** - Interface mais amigável que pip
5. **Compatibilidade** - Funciona com PyPI e ferramentas existentes
