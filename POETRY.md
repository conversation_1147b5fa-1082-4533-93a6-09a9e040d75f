# 🎭 Poetry Setup Guide

Este guia explica como usar Poetry no projeto Media Convert Service.

## 📋 O que é Poetry?

Poetry é uma ferramenta moderna para gerenciamento de dependências e empacotamento em Python que oferece:

- **Resolução de dependências determinística** - Garan<PERSON> builds reproduzíveis
- **Ambiente virtual automático** - Cria e gerencia ambientes virtuais automaticamente
- **Arquivo de lock** - `poetry.lock` garante versões exatas das dependências
- **Comandos simples** - Interface intuitiva para gerenciar dependências
- **Compatibilidade com pip** - Funciona com PyPI e repositórios privados

## 🚀 Instalação

### 1. Instalar Poetry

```bash
# Instalação oficial (recomendada)
curl -sSL https://install.python-poetry.org | python3 -

# Adicionar ao PATH (adicione ao seu ~/.bashrc ou ~/.zshrc)
export PATH="$HOME/.local/bin:$PATH"

# Verificar instalação
poetry --version
```

### 2. Configurar o projeto

```bash
# Instalar dependências do projeto
poetry install

# Ativar ambiente virtual
poetry shell
```

## 🛠️ Comandos Básicos

### Gerenciamento de Dependências

```bash
# Instalar todas as dependências
poetry install

# Instalar apenas dependências de produção
poetry install --only=main

# Atualizar dependências
poetry update

# Adicionar nova dependência
poetry add requests

# Adicionar dependência de desenvolvimento
poetry add --group dev pytest

# Remover dependência
poetry remove requests

# Mostrar dependências instaladas
poetry show

# Mostrar árvore de dependências
poetry show --tree
```

### Ambiente Virtual

```bash
# Ativar ambiente virtual
poetry shell

# Executar comando no ambiente virtual
poetry run python script.py
poetry run pytest
poetry run celery worker

# Mostrar informações do ambiente
poetry env info

# Listar ambientes virtuais
poetry env list

# Remover ambiente virtual
poetry env remove python
```

## 🐳 Docker com Poetry

### Usando Poetry no Docker Compose

```bash
# Construir imagem (agora usa Poetry por padrão)
make build

# Executar worker (agora usa Poetry automaticamente)
make worker

# Logs do worker
docker-compose logs -f media-convert-worker
```

### Dockerfile com Poetry

O projeto usa `docker/Dockerfile.dev` que:

- Instala Poetry na imagem
- Configura Poetry para usar venv no projeto
- Instala dependências usando `poetry install`
- Executa aplicação com `poetry run`

## 📁 Estrutura de Arquivos

```
├── pyproject.toml      # Configuração do Poetry e dependências
├── poetry.lock         # Versões exatas das dependências (não editar)
└── docker/
    ├── Dockerfile.dev      # Dockerfile usando Poetry
    └── entrypoint.sh       # Entrypoint com Poetry
```

## ⚙️ Configuração do pyproject.toml

```toml
[tool.poetry]
name = "media-convert"
version = "1.0.0"
description = "Media Convert Service"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
celery = "5.3.4"
# ... outras dependências

[tool.poetry.group.dev.dependencies]
pytest = "7.4.4"
ruff = "0.1.6"
# ... dependências de desenvolvimento
```

## 🔄 Migração Completa para Poetry

O projeto agora usa **exclusivamente Poetry** para gerenciamento de dependências:

### Uso do Poetry:
```bash
# Instalar dependências
poetry install

# Executar worker (usa Poetry automaticamente)
make worker

# Desenvolvimento local
poetry shell
poetry run python -m app.celery_app
```

## 🎯 Comandos Make

```bash
# Docker (usa Poetry automaticamente)
make build               # Construir imagem
make worker              # Executar worker
make logs                # Ver logs
make down                # Parar serviços
```

## 🎭 Comandos Poetry Diretos

Use os comandos Poetry diretamente - são mais simples e diretos:

```bash
# Gerenciamento de dependências
poetry install           # Instalar dependências
poetry update            # Atualizar dependências
poetry add <package>     # Adicionar dependência
poetry add --group dev <package>  # Adicionar dep. desenvolvimento
poetry remove <package>  # Remover dependência

# Ambiente virtual
poetry shell             # Ativar ambiente virtual
poetry run <command>     # Executar comando no ambiente
```

## 🔧 Troubleshooting

### Problema: "Current Python version not allowed"
```bash
# Verificar versão Python no projeto
poetry env info

# Usar Python específico
poetry env use python3.11
```

### Problema: Dependências conflitantes
```bash
# Limpar cache
poetry cache clear pypi --all

# Reinstalar dependências
poetry install --sync
```

### Problema: Ambiente virtual corrompido
```bash
# Remover ambiente atual
poetry env remove python

# Reinstalar
poetry install
```

## 📚 Recursos Adicionais

- [Documentação oficial do Poetry](https://python-poetry.org/docs/)
- [Guia de migração](https://python-poetry.org/docs/managing-dependencies/)
- [Configuração avançada](https://python-poetry.org/docs/configuration/)

## 🎉 Vantagens do Poetry

1. **Resolução determinística** - Builds reproduzíveis
2. **Gerenciamento automático de venv** - Sem configuração manual
3. **Separação clara** - Dependências de produção vs desenvolvimento
4. **Comandos intuitivos** - Interface mais amigável que pip
5. **Compatibilidade** - Funciona com PyPI e ferramentas existentes
