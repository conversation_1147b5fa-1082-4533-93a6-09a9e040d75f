# Production Docker Compose for Media Convert Service
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

version: '3.8'

services:
  # Media Convert Worker - Production overrides
  media-convert-worker:
    environment:
      # Production logging configuration
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
      - LOG_STRUCTURED=true
      - LOG_FILE_ENABLED=true
      - LOG_FILE_PATH=/var/log/celery/media-convert.log
      - LOG_FILE_ROTATION=100 MB
      - LOG_FILE_RETENTION=30 days
      - FORCE_COLOR=0
      
      # Production Celery configuration
      - CELERY_LOGLEVEL=info
      - CELERY_CONCURRENCY=4
      
      # Production database configuration
      - SQL_ECHO=false
      
    # Production volume mounts
    volumes:
      - ./logs:/var/log/celery:rw
      - ./temp:/tmp/media-convert:rw
      # Remove source code mount for production
      
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Production restart policy
    restart: unless-stopped

  # RabbitMQ - Production configuration
  rabbitmq:
    environment:
      # Production logging
      - RABBITMQ_LOGS=-
      - RABBITMQ_SASL_LOGS=-
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    restart: unless-stopped

  # PostgreSQL - Production configuration  
  postgres:
    environment:
      # Production logging
      - POSTGRES_INITDB_ARGS=--auth-host=md5
      
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    restart: unless-stopped
