# LMS Service - Example Microservice

Este é um exemplo completo de como um microserviço (LMS - Learning Management System) se comunica com o **Media Convert Service** em um cenário real de produção.

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LMS Service   │    │   RabbitMQ      │    │ Media Convert   │
│                 │    │                 │    │    Service      │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ FastAPI     │ │    │ │media-convert│ │    │ │   Worker    │ │
│ │ Endpoints   │ │    │ │  .requests  │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│        │        │    │        ▲        │    │        │        │
│ ┌─────────────┐ │    │        │        │    │        ▼        │
│ │ Celery      │ │────┼────────┘        │    │ ┌─────────────┐ │
│ │ Worker      │ │    │                 │    │ │ Conversion  │ │
│ └─────────────┘ │    │ ┌─────────────┐ │    │ │ Service     │ │
│        ▲        │    │ │lms.conversion│ │    │ └─────────────┘ │
│        │        │    │ │ -responses  │ │    │        │        │
│        └────────┼────┼─┤             │◄┼────┼────────┘        │
│                 │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 Componentes

### **LMS Service (Este Repositório)**
- **FastAPI**: API REST para upload de vídeos
- **Celery Worker**: Processa tasks assíncronas
- **MediaConvertClient**: Cliente para comunicação com Media Convert Service
- **CourseService**: Gerencia cursos e status de vídeos

### **Media Convert Service (Repositório Separado)**
- Serviço dedicado para conversão de vídeos
- Consome da fila `media-convert.requests`
- Envia respostas para filas específicas

## 🚀 Como Executar

### **Pré-requisitos**
1. Media Convert Service deve estar rodando
2. RabbitMQ compartilhado entre os serviços

### **Passo 1: Iniciar Media Convert Service**
```bash
# No diretório do media-convert
cd /path/to/media-convert
docker-compose up -d
```

### **Passo 2: Iniciar LMS Service**
```bash
# No diretório do lms-service
cd example_lms_service
docker-compose up -d
```

### **Passo 3: Testar Comunicação**
```bash
# Teste completo
python test_microservice_communication.py

# Ou teste via API
curl -X POST "http://localhost:8001/api/videos/upload" \
  -H "Content-Type: application/json" \
  -d '{
    "course_id": "course-123",
    "s3_upload_path": "s3://lms-uploads/video.mp4",
    "video_title": "Aula 1"
  }'
```

## 🔄 Fluxo de Comunicação

### **1. Upload de Vídeo**
```python
# Usuario faz upload via API
POST /api/videos/upload
{
  "course_id": "course-123",
  "s3_upload_path": "s3://lms-uploads/video.mp4",
  "video_title": "Aula 1"
}
```

### **2. LMS Processa Upload**
```python
# LMS Worker executa task
process_uploaded_video.delay(
    course_id="course-123",
    video_id="video-456", 
    s3_upload_path="s3://lms-uploads/video.mp4"
)
```

### **3. LMS Solicita Conversão**
```python
# Envia para media-convert.requests
{
  "conversion_id": "video-456",
  "input_s3_path": "s3://lms-uploads/video.mp4",
  "output_s3_prefix": "s3://lms-converted/course-123/video-456/",
  "reply_to_queue": "lms.conversion-responses"
}
```

### **4. Media Convert Processa**
```python
# Media Convert Service converte o vídeo
# Gera arquivos DASH (240p, 360p, 480p, 720p)
```

### **5. Media Convert Responde**
```python
# Envia para lms.conversion-responses
{
  "conversion_id": "video-456",
  "status": "COMPLETED",
  "output_s3_url": "s3://lms-converted/course-123/video-456/manifest.mpd"
}
```

### **6. LMS Processa Resposta**
```python
# LMS Worker processa resposta
handle_conversion_response.delay(response_data)

# Atualiza status no banco
# Notifica usuário
```

## 📋 Contratos de Mensagem

### **ConversionRequest** (LMS → Media Convert)
```python
{
  "conversion_id": "uuid",
  "input_s3_path": "s3://bucket/input.mp4",
  "output_s3_prefix": "s3://bucket/output/",
  "reply_to_queue": "lms.conversion-responses"
}
```

### **ConversionResponse** (Media Convert → LMS)
```python
{
  "conversion_id": "uuid",
  "status": "COMPLETED|FAILED",
  "output_s3_url": "s3://bucket/output/manifest.mpd",
  "error_message": "error details if failed"
}
```

## 🔧 Configuração

### **Variáveis de Ambiente**
```bash
# RabbitMQ (mesmo broker dos dois serviços)
CELERY_BROKER_URL=pyamqp://admin:admin@rabbitmq:5672/media_convert

# Filas do LMS
CELERY_QUEUES=lms.conversion-responses,lms.video-processing
```

### **Filas RabbitMQ**
- `media-convert.requests` - Requisições para Media Convert
- `lms.conversion-responses` - Respostas para LMS
- `lms.video-processing` - Tasks internas do LMS

## 🎯 Benefícios da Arquitetura

### **✅ Separação de Responsabilidades**
- LMS: Gerencia cursos e usuários
- Media Convert: Especializado em conversão

### **✅ Escalabilidade Independente**
- Cada serviço pode escalar conforme demanda
- Workers podem ser ajustados independentemente

### **✅ Tolerância a Falhas**
- Filas garantem entrega de mensagens
- Retry automático em caso de falha

### **✅ Desenvolvimento Independente**
- Equipes podem trabalhar em paralelo
- Deploys independentes

### **✅ Reutilização**
- Media Convert pode ser usado por outros serviços
- Contratos bem definidos

## 🧪 Testes

```bash
# Teste completo de integração
python test_microservice_communication.py

# Teste apenas API
curl http://localhost:8001/health

# Verificar status de vídeo
curl http://localhost:8001/api/videos/status/{video_id}
```

## 📊 Monitoramento

### **Logs**
- LMS Worker: Processa uploads e respostas
- Media Convert Worker: Converte vídeos
- RabbitMQ: Filas e mensagens

### **Métricas**
- Tempo de conversão
- Taxa de sucesso/falha
- Throughput de vídeos

Este exemplo demonstra como implementar comunicação entre microserviços de forma robusta e escalável em um ambiente real de produção.
