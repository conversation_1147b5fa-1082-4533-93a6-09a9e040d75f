"""
Video processing tasks for LMS Service
"""

import logging
from lms.celery_app import celery_app
from lms.services.media_convert_client import media_convert_client
from lms.services.course_service import course_service

logger = logging.getLogger(__name__)


@celery_app.task
def process_uploaded_video(course_id: str, video_id: str, s3_upload_path: str):
    """
    Process a newly uploaded video for a course
    
    Args:
        course_id: ID of the course
        video_id: ID of the video
        s3_upload_path: S3 path where the video was uploaded
    """
    logger.info(
        f"Processing uploaded video: {video_id} for course: {course_id}",
        extra={"course_id": course_id, "video_id": video_id}
    )
    
    try:
        # Generate output path for DASH files
        output_s3_prefix = f"s3://lms-converted-videos/courses/{course_id}/videos/{video_id}/"
        
        # Request conversion from Media Convert Service
        conversion_id = media_convert_client.request_video_conversion(
            input_s3_path=s3_upload_path,
            output_s3_prefix=output_s3_prefix,
            conversion_id=video_id  # Use video_id as conversion_id for tracking
        )
        
        # Update video status in database
        course_service.update_video_conversion_status(
            conversion_id=conversion_id,
            status="PROCESSING",
            input_s3_path=s3_upload_path,
            output_s3_prefix=output_s3_prefix
        )
        
        logger.info(
            f"Video conversion requested successfully: {conversion_id}",
            extra={
                "course_id": course_id,
                "video_id": video_id,
                "conversion_id": conversion_id
            }
        )
        
        return {
            "status": "conversion_requested",
            "conversion_id": conversion_id,
            "course_id": course_id,
            "video_id": video_id
        }
        
    except Exception as e:
        logger.error(
            f"Failed to process uploaded video: {e}",
            extra={
                "course_id": course_id,
                "video_id": video_id,
                "error": str(e)
            }
        )
        
        # Update video status to failed
        course_service.update_video_conversion_status(
            conversion_id=video_id,
            status="FAILED",
            error_message=str(e)
        )
        
        raise


@celery_app.task
def retry_failed_conversion(video_id: str):
    """
    Retry a failed video conversion
    
    Args:
        video_id: ID of the video to retry
    """
    logger.info(f"Retrying failed conversion for video: {video_id}")
    
    try:
        # Get video details from database
        video_info = course_service.get_video_info(video_id)
        
        if not video_info:
            raise ValueError(f"Video not found: {video_id}")
        
        # Request conversion again
        conversion_id = media_convert_client.request_video_conversion(
            input_s3_path=video_info["input_s3_path"],
            output_s3_prefix=video_info["output_s3_prefix"],
            conversion_id=video_id
        )
        
        # Update status to processing
        course_service.update_video_conversion_status(
            conversion_id=conversion_id,
            status="PROCESSING"
        )
        
        logger.info(f"Retry conversion requested: {conversion_id}")
        
        return {
            "status": "retry_requested",
            "conversion_id": conversion_id,
            "video_id": video_id
        }
        
    except Exception as e:
        logger.error(f"Failed to retry conversion: {e}")
        raise
