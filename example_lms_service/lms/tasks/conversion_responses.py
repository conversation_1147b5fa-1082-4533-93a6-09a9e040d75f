"""
Tasks for handling conversion responses from Media Convert Service
"""

import logging
from lms.celery_app import celery_app
from lms.models.messages import ConversionResponse
from lms.services.course_service import course_service

logger = logging.getLogger(__name__)


@celery_app.task
def handle_conversion_response(response_data: dict):
    """
    Handle conversion response from Media Convert Service
    
    This task is triggered when Media Convert Service sends a response
    to our lms.conversion-responses queue.
    
    Args:
        response_data: Dictionary containing conversion response data
    """
    try:
        # Parse the response
        response = ConversionResponse.from_dict(response_data)
        
        logger.info(
            f"Received conversion response: {response.conversion_id}",
            extra={
                "conversion_id": response.conversion_id,
                "status": response.status
            }
        )
        
        if response.is_success():
            # Handle successful conversion
            logger.info(f"Conversion completed successfully: {response.conversion_id}")
            
            # Update course video status in database
            course_service.update_video_conversion_status(
                conversion_id=response.conversion_id,
                status="COMPLETED",
                dash_url=response.output_s3_url
            )
            
            # Notify user that video is ready
            course_service.notify_video_ready(response.conversion_id)
            
        elif response.is_failure():
            # Handle failed conversion
            logger.error(
                f"Conversion failed: {response.conversion_id} - {response.error_message}",
                extra={
                    "conversion_id": response.conversion_id,
                    "error": response.error_message
                }
            )
            
            # Update course video status in database
            course_service.update_video_conversion_status(
                conversion_id=response.conversion_id,
                status="FAILED",
                error_message=response.error_message
            )
            
            # Notify user about the failure
            course_service.notify_video_failed(
                conversion_id=response.conversion_id,
                error_message=response.error_message
            )
        
        return {
            "processed": True,
            "conversion_id": response.conversion_id,
            "status": response.status
        }
        
    except Exception as e:
        logger.error(
            f"Error processing conversion response: {e}",
            extra={"response_data": response_data, "error": str(e)}
        )
        raise
