"""
FastAPI main application for LMS Service
"""

from fastapi import FastAPI
from lms.api.video_endpoints import router as video_router

app = FastAPI(
    title="LMS Service",
    description="Learning Management System with Video Conversion",
    version="1.0.0"
)

# Include routers
app.include_router(video_router)


@app.get("/")
async def root():
    return {
        "service": "LMS Service",
        "status": "running",
        "description": "Learning Management System with integrated video conversion"
    }


@app.get("/health")
async def health_check():
    return {"status": "healthy"}
