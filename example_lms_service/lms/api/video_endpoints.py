"""
FastAPI endpoints for video management
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import uuid

from lms.tasks.video_tasks import process_uploaded_video, retry_failed_conversion
from lms.services.course_service import course_service

router = APIRouter(prefix="/api/videos", tags=["videos"])


class VideoUploadRequest(BaseModel):
    course_id: str
    s3_upload_path: str
    video_title: str


class VideoStatusResponse(BaseModel):
    video_id: str
    status: str
    dash_url: Optional[str] = None
    error_message: Optional[str] = None


@router.post("/upload", response_model=dict)
async def upload_video(request: VideoUploadRequest):
    """
    Process a newly uploaded video
    
    This endpoint is called after a video file has been uploaded to S3.
    It triggers the conversion process via the Media Convert Service.
    """
    try:
        # Generate unique video ID
        video_id = str(uuid.uuid4())
        
        # Trigger video processing task
        task = process_uploaded_video.delay(
            course_id=request.course_id,
            video_id=video_id,
            s3_upload_path=request.s3_upload_path
        )
        
        return {
            "message": "Video processing started",
            "video_id": video_id,
            "course_id": request.course_id,
            "task_id": task.id,
            "status": "PROCESSING"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{video_id}", response_model=VideoStatusResponse)
async def get_video_status(video_id: str):
    """Get the conversion status of a video"""
    try:
        video_info = course_service.get_video_info(video_id)
        
        if not video_info:
            raise HTTPException(status_code=404, detail="Video not found")
        
        return VideoStatusResponse(
            video_id=video_id,
            status=video_info.get("status", "UNKNOWN"),
            dash_url=video_info.get("dash_url"),
            error_message=video_info.get("error_message")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/retry/{video_id}")
async def retry_video_conversion(video_id: str):
    """Retry a failed video conversion"""
    try:
        video_info = course_service.get_video_info(video_id)
        
        if not video_info:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if video_info.get("status") != "FAILED":
            raise HTTPException(
                status_code=400, 
                detail="Can only retry failed conversions"
            )
        
        # Trigger retry task
        task = retry_failed_conversion.delay(video_id)
        
        return {
            "message": "Retry initiated",
            "video_id": video_id,
            "task_id": task.id,
            "status": "PROCESSING"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list/{status}")
async def list_videos_by_status(status: str):
    """List videos by conversion status"""
    try:
        videos = course_service.list_videos_by_status(status)
        return {
            "status": status,
            "count": len(videos),
            "videos": videos
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
