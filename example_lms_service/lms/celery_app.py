"""
Celery configuration for LMS Service
"""

from celery import Celery
from decouple import config

# RabbitMQ configuration (same broker as media-convert service)
CELERY_BROKER_URL = config(
    "CELERY_BROKER_URL", 
    default="pyamqp://admin:admin@localhost:5672/media_convert"
)

# Create Celery app
celery_app = Celery(
    "lms-service",
    broker=CELERY_BROKER_URL,
    backend="rpc://",
    include=["lms.tasks.video_tasks", "lms.tasks.conversion_responses"],
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
)

# Queue routing
celery_app.conf.task_routes = {
    # Tasks for handling conversion responses from media-convert service
    "lms.tasks.conversion_responses.*": {"queue": "lms.conversion-responses"},
    
    # Internal LMS tasks
    "lms.tasks.video_tasks.*": {"queue": "lms.video-processing"},
}
