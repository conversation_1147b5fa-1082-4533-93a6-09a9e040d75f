"""
Course service for managing course videos and conversions
(Mock implementation for demonstration)
"""

import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class CourseService:
    """Service for managing course videos and their conversion status"""
    
    def __init__(self):
        # In a real implementation, this would be a database
        self.videos_db = {}
    
    def update_video_conversion_status(
        self,
        conversion_id: str,
        status: str,
        dash_url: Optional[str] = None,
        error_message: Optional[str] = None,
        input_s3_path: Optional[str] = None,
        output_s3_prefix: Optional[str] = None
    ):
        """Update video conversion status in database"""
        
        if conversion_id not in self.videos_db:
            self.videos_db[conversion_id] = {}
        
        video_record = self.videos_db[conversion_id]
        video_record.update({
            "conversion_id": conversion_id,
            "status": status,
            "updated_at": "2025-01-19T12:00:00Z"  # Mock timestamp
        })
        
        if dash_url:
            video_record["dash_url"] = dash_url
        if error_message:
            video_record["error_message"] = error_message
        if input_s3_path:
            video_record["input_s3_path"] = input_s3_path
        if output_s3_prefix:
            video_record["output_s3_prefix"] = output_s3_prefix
        
        logger.info(
            f"Updated video conversion status: {conversion_id} -> {status}",
            extra={
                "conversion_id": conversion_id,
                "status": status,
                "dash_url": dash_url
            }
        )
    
    def get_video_info(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video information from database"""
        return self.videos_db.get(video_id)
    
    def notify_video_ready(self, conversion_id: str):
        """Notify user that video conversion is complete"""
        logger.info(
            f"📧 Notifying user: Video {conversion_id} is ready for viewing!",
            extra={"conversion_id": conversion_id}
        )
        
        # In a real implementation, this would:
        # - Send email notification
        # - Send push notification
        # - Update UI in real-time via WebSocket
        # - Create notification record in database
        
        print(f"🎉 VIDEO READY: {conversion_id}")
        print(f"📱 User notification sent for conversion: {conversion_id}")
    
    def notify_video_failed(self, conversion_id: str, error_message: str):
        """Notify user that video conversion failed"""
        logger.error(
            f"📧 Notifying user: Video {conversion_id} conversion failed",
            extra={"conversion_id": conversion_id, "error": error_message}
        )
        
        # In a real implementation, this would:
        # - Send error notification to user
        # - Log error for admin review
        # - Possibly trigger retry logic
        # - Update UI to show error state
        
        print(f"❌ VIDEO FAILED: {conversion_id}")
        print(f"📱 Error notification sent: {error_message}")
    
    def list_videos_by_status(self, status: str) -> list:
        """List videos by conversion status"""
        return [
            video for video in self.videos_db.values()
            if video.get("status") == status
        ]


# Global instance
course_service = CourseService()
