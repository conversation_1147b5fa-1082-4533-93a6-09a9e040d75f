"""
Client service for communicating with Media Convert Service
"""

import logging
from typing import Optional
from lms.celery_app import celery_app
from lms.models.messages import ConversionRequest

logger = logging.getLogger(__name__)


class MediaConvertClient:
    """Client for sending video conversion requests to Media Convert Service"""
    
    def __init__(self):
        self.media_convert_queue = "media-convert.requests"
        self.lms_response_queue = "lms.conversion-responses"
    
    def request_video_conversion(
        self,
        input_s3_path: str,
        output_s3_prefix: str,
        conversion_id: Optional[str] = None
    ) -> str:
        """
        Request video conversion from Media Convert Service
        
        Args:
            input_s3_path: S3 URL of the input video file
            output_s3_prefix: S3 prefix for output DASH files
            conversion_id: Optional custom conversion ID
            
        Returns:
            str: Conversion ID for tracking
        """
        # Create conversion request
        request = ConversionRequest.create(
            input_s3_path=input_s3_path,
            output_s3_prefix=output_s3_prefix,
            reply_to_queue=self.lms_response_queue,
            conversion_id=conversion_id
        )
        
        logger.info(
            f"Requesting video conversion: {request.conversion_id}",
            extra={
                "conversion_id": request.conversion_id,
                "input_s3_path": input_s3_path,
                "output_s3_prefix": output_s3_prefix
            }
        )
        
        try:
            # Send request to Media Convert Service
            celery_app.send_task(
                "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
                args=[request.to_dict()],
                queue=self.media_convert_queue
            )
            
            logger.info(f"Conversion request sent successfully: {request.conversion_id}")
            return request.conversion_id
            
        except Exception as e:
            logger.error(
                f"Failed to send conversion request: {e}",
                extra={"conversion_id": request.conversion_id, "error": str(e)}
            )
            raise
    
    def get_response_queue(self) -> str:
        """Get the queue name where responses will be received"""
        return self.lms_response_queue


# Global instance
media_convert_client = MediaConvertClient()
