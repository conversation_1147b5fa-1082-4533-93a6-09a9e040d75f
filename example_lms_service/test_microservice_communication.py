#!/usr/bin/env python3
"""
Test script to demonstrate communication between LMS Service and Media Convert Service

This script simulates a real-world scenario where:
1. A user uploads a video to the LMS
2. LMS requests conversion from Media Convert Service
3. Media Convert Service processes the video
4. Media Convert Service sends response back to LMS
5. LMS updates the video status and notifies the user
"""

import time
import requests
import json
from lms.services.media_convert_client import media_convert_client
from lms.tasks.video_tasks import process_uploaded_video


def test_end_to_end_flow():
    """Test the complete end-to-end flow"""
    
    print("🎬 LMS Service - Media Convert Service Integration Test")
    print("=" * 60)
    
    # Simulate a course and video upload
    course_id = "course-123"
    video_title = "Introduction to Python"
    s3_upload_path = "s3://lms-uploads/courses/course-123/raw-video.mp4"
    
    print(f"📚 Course ID: {course_id}")
    print(f"🎥 Video Title: {video_title}")
    print(f"📁 Uploaded to: {s3_upload_path}")
    print()
    
    # Step 1: Trigger video processing (simulates API call)
    print("🚀 Step 1: Triggering video processing...")
    
    task = process_uploaded_video.delay(
        course_id=course_id,
        video_id="video-456",
        s3_upload_path=s3_upload_path
    )
    
    print(f"✅ Video processing task started: {task.id}")
    print(f"📋 Task will request conversion from Media Convert Service")
    print()
    
    # Step 2: Show what happens in the background
    print("🔄 Step 2: Background Process Flow...")
    print("   1. LMS Worker picks up the task")
    print("   2. LMS sends conversion request to 'media-convert.requests' queue")
    print("   3. Media Convert Service processes the video")
    print("   4. Media Convert Service sends response to 'lms.conversion-responses' queue")
    print("   5. LMS Worker processes the response")
    print("   6. LMS updates video status and notifies user")
    print()
    
    # Step 3: Direct client test
    print("🧪 Step 3: Testing direct client communication...")
    
    conversion_id = media_convert_client.request_video_conversion(
        input_s3_path=s3_upload_path,
        output_s3_prefix="s3://lms-converted-videos/courses/course-123/videos/video-456/"
    )
    
    print(f"✅ Conversion request sent successfully!")
    print(f"🆔 Conversion ID: {conversion_id}")
    print(f"📬 Response will be sent to: {media_convert_client.get_response_queue()}")
    print()
    
    # Step 4: Show expected response format
    print("📨 Step 4: Expected Response Format...")
    
    expected_success_response = {
        "conversion_id": conversion_id,
        "status": "COMPLETED",
        "output_s3_url": "s3://lms-converted-videos/courses/course-123/videos/video-456/manifest.mpd",
        "error_message": None
    }
    
    expected_failure_response = {
        "conversion_id": conversion_id,
        "status": "FAILED",
        "output_s3_url": None,
        "error_message": "S3 file not found"
    }
    
    print("✅ Success Response:")
    print(json.dumps(expected_success_response, indent=2))
    print()
    
    print("❌ Failure Response:")
    print(json.dumps(expected_failure_response, indent=2))
    print()
    
    # Step 5: Integration summary
    print("📊 Step 5: Integration Summary")
    print("=" * 40)
    print("🔗 Communication Pattern:")
    print("   LMS Service → media-convert.requests → Media Convert Service")
    print("   Media Convert Service → lms.conversion-responses → LMS Service")
    print()
    print("📦 Shared Message Contracts:")
    print("   • ConversionRequest (LMS → Media Convert)")
    print("   • ConversionResponse (Media Convert → LMS)")
    print()
    print("🏗️ Architecture Benefits:")
    print("   • ✅ Loose coupling - services are independent")
    print("   • ✅ Async processing - non-blocking operations")
    print("   • ✅ Scalability - each service can scale independently")
    print("   • ✅ Reliability - message queues provide durability")
    print("   • ✅ Monitoring - each service can be monitored separately")
    print()
    
    return {
        "task_id": task.id,
        "conversion_id": conversion_id,
        "course_id": course_id,
        "status": "test_completed"
    }


def test_api_endpoints():
    """Test the LMS API endpoints (requires running API server)"""
    
    print("🌐 Testing LMS API Endpoints")
    print("=" * 30)
    
    base_url = "http://localhost:8001"
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/health")
        print(f"✅ Health Check: {response.status_code} - {response.json()}")
        
        # Test video upload endpoint
        upload_data = {
            "course_id": "course-789",
            "s3_upload_path": "s3://lms-uploads/test-video.mp4",
            "video_title": "Test Video"
        }
        
        response = requests.post(f"{base_url}/api/videos/upload", json=upload_data)
        print(f"✅ Video Upload: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            video_id = result["video_id"]
            print(f"   Video ID: {video_id}")
            
            # Test status endpoint
            time.sleep(1)  # Give it a moment
            status_response = requests.get(f"{base_url}/api/videos/status/{video_id}")
            print(f"✅ Video Status: {status_response.status_code} - {status_response.json()}")
        
    except requests.exceptions.ConnectionError:
        print("⚠️  API server not running. Start with: docker-compose up lms-api")
    except Exception as e:
        print(f"❌ API test failed: {e}")


if __name__ == "__main__":
    print("🎯 Choose test mode:")
    print("1. End-to-end flow test")
    print("2. API endpoints test")
    print("3. Both")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice in ["1", "3"]:
        result = test_end_to_end_flow()
        print(f"🎉 Test completed: {result}")
    
    if choice in ["2", "3"]:
        print("\n" + "="*60 + "\n")
        test_api_endpoints()
    
    print("\n🏁 All tests completed!")
