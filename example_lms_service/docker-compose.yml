version: '3.8'

services:
  lms-worker:
    build: .
    container_name: lms-worker
    environment:
      - CELERY_BROKER_URL=pyamqp://admin:admin@rabbitmq:5672/media_convert
      - CELERY_QUEUES=lms.conversion-responses,lms.video-processing
    command: celery -A lms.celery_app worker --queues=lms.conversion-responses,lms.video-processing --loglevel=info --concurrency=2
    volumes:
      - .:/app
    working_dir: /app
    depends_on:
      - rabbitmq
    networks:
      - lms-network
      - media-convert_media-convert-network  # Connect to media-convert network

  lms-api:
    build: .
    container_name: lms-api
    ports:
      - "8001:8000"
    environment:
      - CELERY_BROKER_URL=pyamqp://admin:admin@rabbitmq:5672/media_convert
    command: uvicorn lms.main:app --host 0.0.0.0 --port 8000 --reload
    volumes:
      - .:/app
    working_dir: /app
    depends_on:
      - rabbitmq
    networks:
      - lms-network
      - media-convert_media-convert-network  # Connect to media-convert network

  # Use the same RabbitMQ as media-convert service
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: lms-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
    ports:
      - "15673:15672"  # Different port to avoid conflict
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - lms-network
      - media-convert_media-convert-network  # Connect to media-convert network

networks:
  lms-network:
    driver: bridge
  media-convert_media-convert-network:
    external: true  # Use existing network from media-convert service

volumes:
  rabbitmq_data:
