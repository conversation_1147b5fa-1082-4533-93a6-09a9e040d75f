# Production Dockerfile for Media Convert Service
FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    gosu \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Poetry
RUN pip install --no-cache-dir poetry==1.7.1

# Configure Poetry for production
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=0 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Create celery user
RUN useradd --create-home --shell /bin/bash --uid 1000 celery

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /var/log/celery /tmp/media-convert \
    && chown -R celery:celery /var/log/celery /tmp/media-convert

# Copy Poetry configuration files
COPY pyproject.toml poetry.lock* ./

# Install only production dependencies
RUN poetry install --only=main --no-root && rm -rf $POETRY_CACHE_DIR

# Copy application code
COPY app/ ./app/
COPY docker/entrypoint.sh /entrypoint.sh

# Set permissions
RUN chmod +x /entrypoint.sh \
    && chown -R celery:celery /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD poetry run celery -A app.celery_app inspect ping || exit 1

# Use our custom entrypoint
ENTRYPOINT ["/entrypoint.sh"]
