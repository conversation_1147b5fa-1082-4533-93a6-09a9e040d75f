"""Media Conversion Repository for database operations"""

from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy.exc import SQLAlchemyError

from app.database.connection import get_db_session
from app.database.models import MediaConversion, MediaType, OutputFormat
from app.utils.logger import get_logger

logger = get_logger(__name__)


class MediaConversionRepository:
    """Repository for managing media conversion records in the database"""

    @staticmethod
    def create_conversion(
        conversion_id: str,
        input_path: str,
        media_type: MediaType,
        output_format: Optional[OutputFormat] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[MediaConversion]:
        """
        Create a new media conversion record

        Args:
            conversion_id: Unique conversion identifier from message payload
            input_path: Path to the input media file
            media_type: Type of media (video, image, audio)
            output_format: Target output format (dash, webp, etc.)
            metadata: Optional metadata dictionary

        Returns:
            MediaConversion object or None if failed
        """
        try:
            with next(get_db_session()) as session:
                conversion = MediaConversion(
                    id=conversion_id,
                    input_path=input_path,
                    media_type=media_type.value,
                    output_format=output_format.value if output_format else None,
                    status="PENDING",
                    metadata_json=metadata or {},
                )

                session.add(conversion)
                session.commit()
                session.refresh(conversion)

                logger.info(f"Created {media_type.value} conversion record: {conversion.id}")
                return conversion

        except SQLAlchemyError as e:
            # Check if it's a duplicate key error
            error_str = str(e).lower()
            if "duplicate key" in error_str or "unique constraint" in error_str:
                logger.warning(f"Conversion {conversion_id} already exists - duplicate request ignored")
                return None
            else:
                logger.error(f"Failed to create conversion record for {conversion_id}: {e}")
                return None

    @staticmethod
    def update_status(
        conversion_id: str,
        status: str,
        output_path: Optional[str] = None,
        processing_duration_seconds: Optional[int] = None,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Update conversion status and details

        Args:
            conversion_id: Conversion identifier
            status: New status (PENDING, PROCESSING, COMPLETED, FAILED)
            output_path: Path to output file (for COMPLETED status)
            processing_duration_seconds: Processing duration in seconds
            error_message: Error details (for FAILED status)
            metadata: Additional metadata to merge

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            with next(get_db_session()) as session:
                conversion = (
                    session.query(MediaConversion).filter_by(id=conversion_id).first()
                )

                if not conversion:
                    logger.error(f"Conversion record not found: {conversion_id}")
                    return False

                # Update fields
                conversion.status = status
                conversion.updated_at = datetime.utcnow()

                if output_path:
                    conversion.output_path = output_path

                if processing_duration_seconds is not None:
                    conversion.processing_duration_seconds = processing_duration_seconds

                if error_message:
                    conversion.error_message = error_message

                # Merge metadata
                if metadata:
                    current_metadata = conversion.metadata_json or {}
                    current_metadata.update(metadata)
                    conversion.metadata_json = current_metadata

                session.commit()

                logger.info(f"Updated conversion {conversion.id} to status: {status}")
                return True

        except SQLAlchemyError as e:
            logger.error(f"Failed to update conversion {conversion_id}: {e}")
            return False

    @staticmethod
    def get_conversion(conversion_id: str) -> Optional[MediaConversion]:
        """
        Get conversion record by conversion ID

        Args:
            conversion_id: Conversion identifier

        Returns:
            MediaConversion object or None if not found
        """
        try:
            with next(get_db_session()) as session:
                conversion = (
                    session.query(MediaConversion).filter_by(id=conversion_id).first()
                )

                if conversion:
                    # Detach from session to avoid lazy loading issues
                    session.expunge(conversion)

                return conversion

        except SQLAlchemyError as e:
            logger.error(f"Failed to get conversion {conversion_id}: {e}")
            return None



    @staticmethod
    def list_conversions(
        status: Optional[str] = None,
        media_type: Optional[MediaType] = None,
        limit: int = 100,
        offset: int = 0
    ) -> list[MediaConversion]:
        """
        List conversion records with optional filtering

        Args:
            status: Filter by status (optional)
            media_type: Filter by media type (optional)
            limit: Maximum number of records
            offset: Number of records to skip

        Returns:
            List of MediaConversion objects
        """
        try:
            with next(get_db_session()) as session:
                query = session.query(MediaConversion)

                if status:
                    query = query.filter_by(status=status)

                if media_type:
                    query = query.filter_by(media_type=media_type.value)

                conversions = (
                    query.order_by(MediaConversion.created_at.desc())
                    .offset(offset)
                    .limit(limit)
                    .all()
                )

                # Detach from session
                for conversion in conversions:
                    session.expunge(conversion)

                return conversions

        except SQLAlchemyError as e:
            logger.error(f"Failed to list conversions: {e}")
            return []
