"""
Simple logging configuration for Media Convert Service using Loguru
"""
import os
import sys

from decouple import config
from loguru import logger

# Simple configuration
LOG_LEVEL = config("LOG_LEVEL", default="INFO")

# Remove default handler and add colorized console handler
logger.remove()

os.environ["FORCE_COLOR"] = "1"

logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> | <level>{message}</level>",
    level=LOG_LEVEL,
    colorize=True,
    enqueue=False,
)


def get_logger(name: str = None):
    """Get a configured logger instance."""
    if name:
        return logger.bind(logger_name=name)
    return logger


def log_video_conversion_metrics(
    task_id: str,
    input_file_size: int,
    output_file_size: int,
    duration: float,
    bitrates: list,
    resolution: str,
):
    """Log video conversion metrics for monitoring."""
    logger.info(
        "Video conversion metrics",
        extra={
            "task_id": task_id,
            "input_file_size_bytes": input_file_size,
            "output_file_size_bytes": output_file_size,
            "conversion_duration_seconds": duration,
            "bitrates": bitrates,
            "resolution": resolution,
            "compression_ratio": output_file_size / input_file_size
            if input_file_size > 0
            else 0,
            "event_type": "video_metrics",
        },
    )
