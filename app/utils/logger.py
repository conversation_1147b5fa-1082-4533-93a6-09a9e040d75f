"""
Simple logging configuration for Media Convert Service using Loguru
"""
import os
import sys

from loguru import logger

try:
    from app.config import LoggingConfig
    LOG_LEVEL = LoggingConfig.LEVEL
    FORCE_COLOR = LoggingConfig.FORCE_COLOR
except ImportError:
    # Fallback for scripts that run outside app context
    from decouple import config
    LOG_LEVEL = config("LOG_LEVEL", default="INFO")
    FORCE_COLOR = config("FORCE_COLOR", default="1")

# Remove default handler and add colorized console handler
logger.remove()

os.environ["FORCE_COLOR"] = FORCE_COLOR

logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> | <level>{message}</level>",
    level=LOG_LEVEL,
    colorize=True,
    enqueue=False,
)


def get_logger(name: str = None):
    """Get a configured logger instance."""
    if name:
        return logger.bind(logger_name=name)
    return logger
