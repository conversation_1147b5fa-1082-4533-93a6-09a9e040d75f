"""
Logging configuration for Media Convert Service using Loguru
Provides environment-aware logging with appropriate levels and formats
"""
import os
import sys

from loguru import logger

try:
    from app.config import LoggingConfig, AppConfig

    LOG_LEVEL = LoggingConfig.LEVEL
    FORCE_COLOR = LoggingConfig.FORCE_COLOR
    ENVIRONMENT = AppConfig.ENVIRONMENT
    FILE_ENABLED = LoggingConfig.FILE_ENABLED
    FILE_PATH = LoggingConfig.FILE_PATH
    FILE_ROTATION = LoggingConfig.FILE_ROTATION
    FILE_RETENTION = LoggingConfig.FILE_RETENTION
    STRUCTURED = LoggingConfig.STRUCTURED
except ImportError:
    # Fallback for scripts that run outside app context
    from decouple import config

    LOG_LEVEL = config("LOG_LEVEL", default="INFO")
    FORCE_COLOR = config("FORCE_COLOR", default="1")
    ENVIRONMENT = config("ENVIRONMENT", default="development")
    FILE_ENABLED = config("LOG_FILE_ENABLED", default=False, cast=bool)
    FILE_PATH = config("LOG_FILE_PATH", default="/var/log/celery/media-convert.log")
    FILE_ROTATION = config("LOG_FILE_ROTATION", default="100 MB")
    FILE_RETENTION = config("LOG_FILE_RETENTION", default="30 days")
    STRUCTURED = config("LOG_STRUCTURED", default=False, cast=bool)

# Remove default handler
logger.remove()

# Set color environment variable
os.environ["FORCE_COLOR"] = FORCE_COLOR

# Configure format based on environment and structured logging preference
if STRUCTURED or ENVIRONMENT == "production":
    # Structured format for production or when explicitly requested
    log_format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}"
    use_colors = False
else:
    # Development format: Colorized and more readable
    log_format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> | <level>{message}</level>"
    use_colors = True and FORCE_COLOR == "1"

# Add console handler with environment-appropriate configuration
logger.add(
    sys.stdout,
    format=log_format,
    level=LOG_LEVEL,
    colorize=use_colors,
    enqueue=False,
)

# Add file handler if enabled
if FILE_ENABLED or ENVIRONMENT == "production":
    # Ensure log directory exists
    os.makedirs(os.path.dirname(FILE_PATH), exist_ok=True)

    logger.add(
        FILE_PATH,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
        level=LOG_LEVEL,
        rotation=FILE_ROTATION,
        retention=FILE_RETENTION,
        compression="gz",
        enqueue=True,
    )


def get_logger(name: str = None):
    """Get a configured logger instance."""
    # Loguru automatically handles module names in the format string
    # The 'name' parameter is kept for API compatibility but not needed
    return logger
