"""Validation utilities for the media conversion service"""

import uuid
from typing import Union


def is_valid_uuid(value: Union[str, uuid.UUID]) -> bool:
    """
    Validate if a value is a valid UUID.

    Args:
        value: String or UUID object to validate

    Returns:
        True if valid UUID, False otherwise
    """
    if isinstance(value, uuid.UUID):
        return True

    if not isinstance(value, str):
        return False

    try:
        uuid.UUID(value)
        return True
    except (ValueError, TypeError):
        return False


def validate_conversion_id(conversion_id: str) -> None:
    """
    Validate conversion_id and raise ValueError if invalid.

    Args:
        conversion_id: String to validate as UUID

    Raises:
        ValueError: If conversion_id is not a valid UUID
    """
    if not is_valid_uuid(conversion_id):
        raise ValueError(f"conversion_id must be a valid UUID, got: {conversion_id}")
