"""
S3 Client for Media Convert Service

This module provides a robust S3 client with retry logic, error handling,
and logging for downloading and uploading video files.
"""

import concurrent.futures
import os
from pathlib import Path
from typing import Any, Dict, <PERSON><PERSON>

import boto3
from botocore.exceptions import ClientError
from decouple import config
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from app.utils.logger import get_logger

logger = get_logger(__name__)


class S3Client:
    """Robust S3 client with retry logic and error handling."""

    def __init__(self):
        """Initialize S3 client with configuration."""
        self.aws_access_key_id = config("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = config("AWS_SECRET_ACCESS_KEY")
        self.aws_region = config("AWS_REGION", default="us-east-1")

        # Initialize boto3 client
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        )

        logger.info("S3 client initialized", extra={"region": self.aws_region})

    def parse_s3_url(self, s3_url: str) -> tuple[str, str]:
        """
        Parse S3 URL to extract bucket and key.

        Args:
            s3_url (str): S3 URL in format s3://bucket/key

        Returns:
            tuple: (bucket, key)

        Raises:
            ValueError: If URL format is invalid
        """
        if not s3_url.startswith("s3://"):
            raise ValueError(f"Invalid S3 URL format: {s3_url}")

        # Remove s3:// prefix and split
        path = s3_url[5:]
        parts = path.split("/", 1)

        if len(parts) != 2:
            raise ValueError(f"Invalid S3 URL format: {s3_url}")

        bucket, key = parts
        return bucket, key

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ClientError, ConnectionError)),
    )
    def download_file(
        self, s3_url: str, local_path: str, task_id: str = None
    ) -> Dict[str, Any]:
        """
        Download file from S3 with retry logic.

        Args:
            s3_url (str): S3 URL of the file to download
            local_path (str): Local path to save the file
            task_id (str, optional): Task ID for logging

        Returns:
            dict: Download result with metadata

        Raises:
            ClientError: If S3 operation fails
            FileNotFoundError: If S3 object doesn't exist
        """
        bucket, key = self.parse_s3_url(s3_url)

        logger.info(
            "Starting S3 download",
            extra={
                "task_id": task_id,
                "s3_url": s3_url,
                "bucket": bucket,
                "key": key,
                "local_path": local_path,
            },
        )

        try:
            # Get object metadata first
            response = self.s3_client.head_object(Bucket=bucket, Key=key)
            file_size = response["ContentLength"]
            last_modified = response["LastModified"]

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # Download the file
            self.s3_client.download_file(bucket, key, local_path)

            # Verify download
            if not os.path.exists(local_path):
                raise FileNotFoundError(f"Downloaded file not found: {local_path}")

            downloaded_size = os.path.getsize(local_path)
            if downloaded_size != file_size:
                raise ValueError(
                    f"File size mismatch: expected {file_size}, got {downloaded_size}"
                )

            result = {
                "status": "success",
                "s3_url": s3_url,
                "local_path": local_path,
                "file_size": file_size,
                "downloaded_size": downloaded_size,
                "last_modified": last_modified.isoformat(),
                "bucket": bucket,
                "key": key,
            }

            logger.info(
                "S3 download completed", extra={"task_id": task_id, "result": result}
            )

            return result

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "NoSuchKey":
                raise FileNotFoundError(f"S3 object not found: {s3_url}")
            elif error_code == "NoSuchBucket":
                raise FileNotFoundError(f"S3 bucket not found: {bucket}")
            else:
                logger.error(
                    "S3 download failed",
                    extra={
                        "task_id": task_id,
                        "error_code": error_code,
                        "error_message": str(e),
                        "s3_url": s3_url,
                    },
                )
                raise

        except Exception as e:
            logger.error(
                "S3 download failed",
                extra={"task_id": task_id, "error": str(e), "s3_url": s3_url},
            )
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ClientError, ConnectionError)),
    )
    def upload_file(
        self,
        local_path: str,
        s3_url: str,
        task_id: str = None,
        content_type: str = None,
    ) -> Dict[str, Any]:
        """
        Upload file to S3 with retry logic.

        Args:
            local_path (str): Local file path to upload
            s3_url (str): S3 URL destination
            task_id (str, optional): Task ID for logging
            content_type (str, optional): Content type for the file

        Returns:
            dict: Upload result with metadata

        Raises:
            FileNotFoundError: If local file doesn't exist
            ClientError: If S3 operation fails
        """
        if not os.path.exists(local_path):
            raise FileNotFoundError(f"Local file not found: {local_path}")

        bucket, key = self.parse_s3_url(s3_url)
        file_size = os.path.getsize(local_path)

        logger.info(
            "Starting S3 upload",
            extra={
                "task_id": task_id,
                "local_path": local_path,
                "s3_url": s3_url,
                "bucket": bucket,
                "key": key,
                "file_size": file_size,
                "content_type": content_type,
            },
        )

        try:
            # Prepare extra args
            extra_args = {}
            if content_type:
                extra_args["ContentType"] = content_type

            # Upload the file
            self.s3_client.upload_file(local_path, bucket, key, ExtraArgs=extra_args)

            # Verify upload by checking if object exists
            response = self.s3_client.head_object(Bucket=bucket, Key=key)
            uploaded_size = response["ContentLength"]

            if uploaded_size != file_size:
                raise ValueError(
                    f"Upload size mismatch: expected {file_size}, got {uploaded_size}"
                )

            result = {
                "status": "success",
                "local_path": local_path,
                "s3_url": s3_url,
                "file_size": file_size,
                "uploaded_size": uploaded_size,
                "bucket": bucket,
                "key": key,
                "content_type": content_type,
            }

            logger.info(
                "S3 upload completed", extra={"task_id": task_id, "result": result}
            )

            return result

        except Exception as e:
            logger.error(
                "S3 upload failed",
                extra={
                    "task_id": task_id,
                    "error": str(e),
                    "local_path": local_path,
                    "s3_url": s3_url,
                },
            )
            raise

    def _upload_single_file_parallel(
        self, file_info: Tuple[str, str, str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """
        Upload a single file (for parallel processing).

        Args:
            file_info: Tuple of (local_file_path, s3_url, task_id, cdn_headers)

        Returns:
            Dict with upload result
        """
        local_file_path, s3_url, task_id, cdn_headers = file_info

        try:
            # Extract content type from headers
            content_type = cdn_headers.get("ContentType", "application/octet-stream")
            result = self.upload_file(local_file_path, s3_url, task_id, content_type)
            return {
                "status": "success",
                "local_path": local_file_path,
                "s3_url": s3_url,
                "file_size": result["file_size"],
                "upload_time": result.get("upload_time", 0),
                "cdn_optimized": True,
            }
        except Exception as e:
            return {
                "status": "error",
                "local_path": local_file_path,
                "s3_url": s3_url,
                "error": str(e),
            }

    def upload_directory_parallel(
        self, local_dir: str, s3_prefix: str, task_id: str = None, max_workers: int = 10
    ) -> Dict[str, Any]:
        """
        Upload entire directory to S3 using parallel uploads.

        Args:
            local_dir (str): Local directory path
            s3_prefix (str): S3 prefix (bucket/path/)
            task_id (str, optional): Task ID for logging
            max_workers (int): Maximum number of parallel upload threads

        Returns:
            dict: Upload results for all files
        """
        if not os.path.exists(local_dir):
            raise FileNotFoundError(f"Local directory not found: {local_dir}")

        # Parse S3 prefix
        if s3_prefix.startswith("s3://"):
            bucket, prefix = self.parse_s3_url(s3_prefix)
        else:
            # Assume format: bucket/prefix
            parts = s3_prefix.split("/", 1)
            bucket = parts[0]
            prefix = parts[1] if len(parts) > 1 else ""

        # Collect all files to upload
        files_to_upload = []
        for root, dirs, files in os.walk(local_dir):
            for file in files:
                local_file_path = os.path.join(root, file)
                rel_path = os.path.relpath(local_file_path, local_dir)
                s3_key = f"{prefix}/{rel_path}".replace("\\", "/").lstrip("/")
                s3_url = f"s3://{bucket}/{s3_key}"
                cdn_headers = self._get_cdn_optimized_headers(file)
                files_to_upload.append((local_file_path, s3_url, task_id, cdn_headers))

        logger.info(
            "Starting parallel directory upload",
            extra={
                "task_id": task_id,
                "local_dir": local_dir,
                "bucket": bucket,
                "prefix": prefix,
                "file_count": len(files_to_upload),
                "max_workers": max_workers,
            },
        )

        uploaded_files = []
        failed_files = []
        total_size = 0

        try:
            # Upload files in parallel
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=max_workers
            ) as executor:
                # Submit all upload tasks
                future_to_file = {
                    executor.submit(
                        self._upload_single_file_parallel, file_info
                    ): file_info
                    for file_info in files_to_upload
                }

                # Process completed uploads
                for future in concurrent.futures.as_completed(future_to_file):
                    file_info = future_to_file[future]
                    try:
                        result = future.result()
                        if result["status"] == "success":
                            uploaded_files.append(result)
                            total_size += result["file_size"]
                        else:
                            failed_files.append(result)
                            logger.error(
                                "File upload failed",
                                extra={
                                    "task_id": task_id,
                                    "local_path": result["local_path"],
                                    "s3_url": result["s3_url"],
                                    "error": result["error"],
                                },
                            )
                    except Exception as e:
                        failed_files.append(
                            {
                                "status": "error",
                                "local_path": file_info[0],
                                "s3_url": file_info[1],
                                "error": str(e),
                            }
                        )
                        logger.error(
                            "Upload task failed",
                            extra={
                                "task_id": task_id,
                                "local_path": file_info[0],
                                "error": str(e),
                            },
                        )

            result = {
                "status": "success" if len(failed_files) == 0 else "partial",
                "local_dir": local_dir,
                "bucket": bucket,
                "prefix": prefix,
                "uploaded_files": uploaded_files,
                "failed_files": failed_files,
                "total_files": len(uploaded_files),
                "failed_count": len(failed_files),
                "total_size": total_size,
            }

            logger.info(
                "Parallel directory upload completed",
                extra={
                    "task_id": task_id,
                    "uploaded_files": len(uploaded_files),
                    "failed_files": len(failed_files),
                    "total_size": total_size,
                    "bucket": bucket,
                    "prefix": prefix,
                },
            )

            return result

        except Exception as e:
            logger.error(
                "Parallel directory upload failed",
                extra={
                    "task_id": task_id,
                    "local_dir": local_dir,
                    "bucket": bucket,
                    "prefix": prefix,
                    "error": str(e),
                },
            )
            return {
                "status": "error",
                "error": str(e),
                "uploaded_files": uploaded_files,
                "failed_files": failed_files,
                "total_files": len(uploaded_files),
                "failed_count": len(failed_files),
                "total_size": total_size,
            }

    def upload_directory(
        self,
        local_dir: str,
        s3_prefix: str,
        task_id: str = None,
        parallel: bool = True,
        max_workers: int = None,
    ) -> Dict[str, Any]:
        """
        Upload entire directory to S3 (uses parallel upload by default for better performance).

        Args:
            local_dir (str): Local directory path
            s3_prefix (str): S3 prefix (bucket/path/)
            task_id (str, optional): Task ID for logging
            parallel (bool): Use parallel uploads (default: True)
            max_workers (int): Maximum number of parallel upload threads (auto-detected if None)

        Returns:
            dict: Upload results for all files
        """
        # Auto-detect optimal worker count for EKS environment
        if max_workers is None:
            try:
                from app.config import AppConfig
                configured_workers = AppConfig.MAX_UPLOAD_WORKERS
            except ImportError:
                # Fallback for scripts that run outside app context
                from decouple import config
                configured_workers = config("MAX_UPLOAD_WORKERS", default=None)

            if configured_workers is not None:
                max_workers = int(configured_workers)
            else:
                # Auto-detect based on instance type
                cpu_count = os.cpu_count() or 2
                # For m6i.large (2 vCPUs, 12.5 Gbps network), network I/O bound
                # Can use more workers than CPU count for I/O operations
                max_workers = min(
                    cpu_count * 3, 6
                )  # Conservative for pod resource limits

        if parallel:
            return self.upload_directory_parallel(
                local_dir, s3_prefix, task_id, max_workers
            )

        # Fallback to sequential upload
        if not os.path.exists(local_dir):
            raise FileNotFoundError(f"Local directory not found: {local_dir}")

        # Parse S3 prefix
        if s3_prefix.startswith("s3://"):
            bucket, prefix = self.parse_s3_url(s3_prefix)
        else:
            # Assume format: bucket/prefix
            parts = s3_prefix.split("/", 1)
            bucket = parts[0]
            prefix = parts[1] if len(parts) > 1 else ""

        uploaded_files = []
        total_size = 0

        logger.info(
            "Starting sequential directory upload",
            extra={
                "task_id": task_id,
                "local_dir": local_dir,
                "bucket": bucket,
                "prefix": prefix,
            },
        )

        # Walk through directory and upload all files
        for root, dirs, files in os.walk(local_dir):
            for file in files:
                local_file_path = os.path.join(root, file)

                # Calculate relative path
                rel_path = os.path.relpath(local_file_path, local_dir)
                s3_key = f"{prefix}/{rel_path}".replace("\\", "/").lstrip("/")
                s3_url = f"s3://{bucket}/{s3_key}"

                # Determine content type
                content_type = self._get_content_type(file)

                try:
                    result = self.upload_file(
                        local_file_path, s3_url, task_id, content_type
                    )
                    uploaded_files.append(result)
                    total_size += result["file_size"]

                except Exception as e:
                    logger.error(
                        "Failed to upload file",
                        extra={
                            "task_id": task_id,
                            "file": local_file_path,
                            "error": str(e),
                        },
                    )
                    raise

        result = {
            "status": "success",
            "local_dir": local_dir,
            "bucket": bucket,
            "prefix": prefix,
            "uploaded_files": uploaded_files,
            "total_files": len(uploaded_files),
            "total_size": total_size,
        }

        logger.info(
            "Sequential directory upload completed",
            extra={"task_id": task_id, "result": result},
        )

        return result

    def _get_content_type(self, filename: str) -> str:
        """Get content type based on file extension."""
        ext = Path(filename).suffix.lower()
        content_types = {
            ".mp4": "video/mp4",
            ".mpd": "application/dash+xml",
            ".m4s": "video/iso.segment",
            ".webm": "video/webm",
            ".json": "application/json",
            ".xml": "application/xml",
            ".vtt": "text/vtt",
            ".m3u8": "application/vnd.apple.mpegurl",
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".png": "image/png",
            ".webp": "image/webp",
        }
        return content_types.get(ext, "application/octet-stream")

    def _get_cdn_optimized_headers(self, filename: str) -> Dict[str, str]:
        """Get CDN-optimized headers based on file type."""
        ext = Path(filename).suffix.lower()

        if ext == ".mpd":
            # Manifest files: short cache, enable compression
            return {
                "ContentType": "application/dash+xml",
                "CacheControl": "max-age=60, public",
                "Metadata": {"file-type": "manifest", "cdn-compress": "true"},
            }
        elif ext == ".m4s":
            # Segment files: long cache, disable compression
            return {
                "ContentType": "video/iso.segment",
                "CacheControl": "max-age=31536000, public, immutable",
                "Metadata": {"file-type": "segment", "cdn-compress": "false"},
            }
        elif ext in [".json", ".xml", ".vtt"]:
            # Text files: medium cache, enable compression
            return {
                "ContentType": self._get_content_type(filename),
                "CacheControl": "max-age=3600, public",
                "Metadata": {"file-type": "text", "cdn-compress": "true"},
            }
        elif ext in [".jpg", ".jpeg", ".png", ".webp"]:
            # Image files: long cache, no compression (already optimized)
            return {
                "ContentType": self._get_content_type(filename),
                "CacheControl": "max-age=86400, public, immutable",  # 24 hours
                "Metadata": {"file-type": "image", "cdn-compress": "false"},
            }
        else:
            # Default: medium cache
            return {
                "ContentType": self._get_content_type(filename),
                "CacheControl": "max-age=3600, public",
            }


