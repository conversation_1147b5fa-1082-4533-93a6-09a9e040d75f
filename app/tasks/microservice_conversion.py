"""
Microservice conversion tasks - handles requests from other microservices
"""

from typing import Optional

from app.celery_app import celery_app
from app.models.messages import ConversionRequest, ConversionResponse
from app.services.conversion_service import ConversionService
from app.utils.logger import get_logger

logger = get_logger(__name__)


@celery_app.task(bind=True)
def process_conversion_request(
    self,
    conversion_id: str,
    input_s3_path: str,
    output_s3_prefix: str,
    reply_to_queue: str,
    bitrates: Optional[list] = None,
):
    """
    Process a video conversion request from another microservice.
    
    This task accepts the standardized payload and sends response to reply_to_queue.
    
    Args:
        conversion_id (str): Unique conversion identifier from requesting microservice
        input_s3_path (str): S3 URL of the input video file
        output_s3_prefix (str): S3 prefix for output files
        reply_to_queue (str): Queue to send the response to
        bitrates (list, optional): Custom bitrate configurations
    
    Returns:
        dict: Conversion result
    """
    celery_task_id = self.request.id

    logger.info(
        f"Processing microservice conversion request - ID: {conversion_id}, Task: {celery_task_id}",
        extra={
            "conversion_id": conversion_id,
            "celery_task_id": celery_task_id,
            "input_s3_path": input_s3_path,
            "output_s3_prefix": output_s3_prefix,
            "reply_to_queue": reply_to_queue
        }
    )

    # Execute conversion using centralized service
    result = ConversionService.execute_conversion(
        conversion_id=conversion_id,
        input_s3_path=input_s3_path,
        output_s3_prefix=output_s3_prefix,
        celery_task_id=celery_task_id,
        bitrates=bitrates,
        metadata={
            "reply_to_queue": reply_to_queue,
            "source": "microservice_request"
        }
    )

    # Send response based on result
    if result["status"] == "completed":
        response = ConversionResponse.success(
            conversion_id=conversion_id,
            output_s3_url=result["output_path"]
        )
    else:
        response = ConversionResponse.failure(
            conversion_id=conversion_id,
            error_message=result["error"]
        )

    _send_response_to_queue(response, reply_to_queue)

    logger.info(
        f"Microservice conversion {'completed' if result['status'] == 'completed' else 'failed'}: {conversion_id}",
        extra={
            "conversion_id": conversion_id,
            "status": result["status"],
            "duration_seconds": result.get("duration_seconds", 0)
        }
    )

    return result


def _send_response_to_queue(response: ConversionResponse, reply_to_queue: str):
    """
    Send response to the specified queue using Celery.

    Args:
        response: ConversionResponse object
        reply_to_queue: Queue name to send the response to
    """
    try:
        # Use Celery to send the response to the reply queue
        celery_app.send_task(
            "lms.tasks.conversion_responses.handle_conversion_response",
            args=[response.to_dict()],
            queue=reply_to_queue
        )

        logger.info(
            f"Sent response to queue: {reply_to_queue}",
            extra={
                "conversion_id": response.conversion_id,
                "status": response.status,
                "reply_to_queue": reply_to_queue
            }
        )

    except Exception as e:
        logger.error(
            f"Failed to send response to queue: {reply_to_queue} - {e}",
            extra={
                "conversion_id": response.conversion_id,
                "error": str(e)
            }
        )


@celery_app.task
def handle_conversion_request_from_dict(request_data: dict):
    """
    Handle conversion request from dictionary payload.
    
    This is the main entry point for other microservices.
    
    Args:
        request_data: Dictionary containing ConversionRequest data
    """
    try:
        # Parse the request
        request = ConversionRequest.from_dict(request_data)
        
        logger.info(
            f"Received microservice conversion request: {request}"
        )
        
        logger.info(
            f"Received microservice conversion request: {request.conversion_id}",
            extra={"conversion_id": request.conversion_id}
        )
        
        # Trigger the conversion task
        task = process_conversion_request.delay(
            conversion_id=request.conversion_id,
            input_s3_path=request.input_s3_path,
            output_s3_prefix=request.output_s3_prefix,
            reply_to_queue=request.reply_to_queue
        )
        
        logger.info(
            f"Triggered conversion task: {task.id} for request: {request.conversion_id}",
            extra={
                "conversion_id": request.conversion_id,
                "celery_task_id": task.id
            }
        )
        
        return {
            "status": "accepted",
            "conversion_id": request.conversion_id,
            "celery_task_id": task.id
        }
        
    except Exception as e:
        logger.error(f"Failed to handle conversion request: {e}")
        raise
