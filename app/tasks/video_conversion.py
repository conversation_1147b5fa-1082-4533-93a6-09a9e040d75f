"""
Video conversion tasks for Media Convert Service
"""

import os
import shutil
import tempfile
import time
from typing import Optional

from app.celery_app import celery_app
from app.services.conversion_service import ConversionService
from app.services.video_conversion_service import VideoConversionService
from app.tasks.notification import send_completion_notification, send_error_notification
from app.utils.ffmpeg_wrapper import FFmpegWrapper
from app.utils.logger import get_logger, log_video_conversion_metrics
from app.utils.s3_client import S3Client

logger = get_logger(__name__)


@celery_app.task(bind=True)
def convert_video_to_dash(
    self,
    input_s3_url: str,
    output_s3_prefix: str,
    webhook_url: Optional[str] = None,
    bitrates: Optional[list] = None,
):
    """
    Convert video to MPEG-DASH format.

    Args:
        input_s3_url (str): S3 URL of the input video file
        output_s3_prefix (str): S3 prefix for output files
        webhook_url (str, optional): Webhook URL for notifications
        bitrates (list, optional): Custom bitrate configurations

    Returns:
        dict: Conversion result with status and output URLs
    """
    celery_task_id = self.request.id
    temp_dir = None
    start_time = time.time()

    # Generate job_id from task_id for tracking
    job_id = celery_task_id

    logger.info(
        f"Video conversion task started - Job: {job_id}, Task: {celery_task_id}"
    )
    logger.info(f"Input: {input_s3_url}, Output prefix: {output_s3_prefix}")

    # Create conversion record in database
    try:
        conversion_record = VideoConversionService.create_conversion(
            job_id=job_id,
            input_path=input_s3_url,
            metadata={
                "celery_task_id": celery_task_id,
                "started_at": start_time,
                "output_prefix": output_s3_prefix,
            },
        )
        conversion_id = conversion_record.id

        # Update status to PROCESSING
        VideoConversionService.update_status(
            job_id=job_id,
            status="PROCESSING",
            metadata={"processing_started_at": start_time},
        )
    except Exception as e:
        logger.error(f"Failed to create conversion record: {e}")
        # Continue without database tracking if creation fails
        conversion_id = None

    try:
        # Initialize clients
        s3_client = S3Client()
        ffmpeg = FFmpegWrapper()

        # Create temporary directory for processing
        temp_dir = tempfile.mkdtemp(prefix=f"media_convert_{job_id}_")
        input_file = os.path.join(temp_dir, "input.mp4")
        output_dir = os.path.join(temp_dir, "output")

        logger.info(f"Created temporary directory: {temp_dir}")

        # Step 1: Download video from S3
        logger.info(f"Downloading video from S3: {input_s3_url}")
        download_result = s3_client.download_file(input_s3_url, input_file, job_id)

        # Step 2: Validate input file
        logger.info("Validating input file")
        ffmpeg.validate_input_file(input_file, job_id)

        # Step 3: Convert to DASH (thumbnails generated automatically)
        logger.info("Converting video to DASH with thumbnails")
        conversion_result = ffmpeg.convert_to_dash(
            input_file, output_dir, job_id, bitrates
        )

        # Step 4: Upload DASH files and thumbnails to S3
        logger.info(f"Uploading DASH files to S3: {output_s3_prefix}")
        upload_result = s3_client.upload_directory(output_dir, output_s3_prefix, job_id)

        # Calculate processing duration
        end_time = time.time()
        duration_seconds = int(end_time - start_time)

        # Prepare output path
        output_path = f"{output_s3_prefix}/manifest.mpd"

        # Prepare final result
        result = {
            "status": "success",
            "job_id": job_id,
            "conversion_id": conversion_id,
            "celery_task_id": celery_task_id,
            "input_path": input_s3_url,
            "output_path": output_path,
            "duration_seconds": duration_seconds,
            "dash_manifest_url": output_path,
            "download_info": download_result,
            "conversion_info": {
                "input_info": conversion_result["input_info"],
                "bitrates_used": conversion_result["bitrates_used"],
                "total_files": conversion_result["total_files"],
                "total_size": conversion_result["total_size"],
            },
            "upload_info": upload_result,
            "message": "Video conversion completed successfully",
        }

        # Update database with success
        if conversion_id:
            try:
                VideoConversionService.update_status(
                    job_id=job_id,
                    status="COMPLETED",
                    output_path=output_path,
                    duration_seconds=duration_seconds,
                    metadata={
                        "completed_at": end_time,
                        "conversion_info": result["conversion_info"],
                        "upload_info": upload_result,
                    },
                )
            except Exception as e:
                logger.error(f"Failed to update conversion status: {e}")

        # Log conversion metrics
        log_video_conversion_metrics(
            task_id=job_id,
            input_file_size=download_result["file_size"],
            output_file_size=upload_result["total_size"],
            duration=conversion_result["input_info"]["duration"],
            bitrates=[br["video_bitrate"] for br in conversion_result["bitrates_used"]],
            resolution=f"{conversion_result['input_info']['video']['width']}x{conversion_result['input_info']['video']['height']}",
        )

        logger.info(
            f"Video conversion completed successfully - Job: {job_id}, Duration: {duration_seconds}s"
        )

        # Send success notification
        if webhook_url:
            send_completion_notification.delay(result, webhook_url)

        return result

    except Exception as e:
        # Calculate duration even for failed conversions
        end_time = time.time()
        duration_seconds = int(end_time - start_time)
        error_message = str(e)

        # Update database with failure
        if conversion_id:
            try:
                VideoConversionService.update_status(
                    job_id=job_id,
                    status="FAILED",
                    duration_seconds=duration_seconds,
                    error_message=error_message,
                    metadata={"failed_at": end_time, "celery_task_id": celery_task_id},
                )
            except Exception as db_error:
                logger.error(f"Failed to update conversion status: {db_error}")

        error_info = {
            "job_id": job_id,
            "conversion_id": conversion_id,
            "celery_task_id": celery_task_id,
            "error": error_message,
            "input_path": input_s3_url,
            "duration_seconds": duration_seconds,
        }

        logger.error(f"Video conversion failed - Job: {job_id}, Error: {error_message}")

        # Send error notification
        if webhook_url:
            send_error_notification.delay(error_info, webhook_url)

        raise

    finally:
        # Cleanup temporary directory
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up temporary directory: {temp_dir}")
            except Exception as e:
                logger.warning(f"Failed to cleanup temporary directory {temp_dir}: {e}")


@celery_app.task(bind=True)
def convert_video_to_dash_v2(
    self,
    input_s3_url: str,
    output_s3_prefix: str,
    webhook_url: Optional[str] = None,
    bitrates: Optional[list] = None,
):
    """
    Convert video to MPEG-DASH format using centralized conversion service.

    This is the new version that uses the centralized ConversionService.

    Args:
        input_s3_url (str): S3 URL of the input video file
        output_s3_prefix (str): S3 prefix for output files
        webhook_url (str, optional): Webhook URL for notifications
        bitrates (list, optional): Custom bitrate configurations

    Returns:
        dict: Conversion result with status and output URLs
    """
    celery_task_id = self.request.id
    start_time = time.time()

    # Generate job_id from task_id for tracking (legacy compatibility)
    job_id = celery_task_id

    logger.info(
        f"Video conversion task v2 started - Job: {job_id}, Task: {celery_task_id}"
    )
    logger.info(f"Input: {input_s3_url}, Output prefix: {output_s3_prefix}")

    try:
        # Execute conversion using centralized service
        result = ConversionService.execute_conversion_legacy(
            input_s3_url=input_s3_url,
            output_s3_prefix=output_s3_prefix,
            celery_task_id=celery_task_id,
            bitrates=bitrates
        )

        # Handle successful conversion
        if result["status"] == "completed":
            logger.info(
                f"Video conversion v2 completed successfully - Job: {job_id}, Duration: {result['duration_seconds']}s"
            )

            # Send completion notification
            if webhook_url:
                send_completion_notification.delay(result, webhook_url)

            # Return legacy-compatible result format
            return {
                "status": "success",
                "job_id": job_id,
                "conversion_id": result.get("conversion_id"),
                "task_id": celery_task_id,
                "output_path": result["output_path"],
                "duration_seconds": result["duration_seconds"],
                "conversion_details": result.get("conversion_details"),
                "upload_details": result.get("upload_details"),
                "message": "Video conversion completed successfully",
            }

        else:
            # Handle failed conversion
            error_message = result["error"]
            duration_seconds = result["duration_seconds"]

            error_info = {
                "job_id": job_id,
                "conversion_id": result.get("conversion_id"),
                "celery_task_id": celery_task_id,
                "error": error_message,
                "input_path": input_s3_url,
                "duration_seconds": duration_seconds,
            }

            logger.error(f"Video conversion v2 failed - Job: {job_id}, Error: {error_message}")

            # Send error notification
            if webhook_url:
                send_error_notification.delay(error_info, webhook_url)

            # Raise exception to maintain legacy behavior
            raise Exception(error_message)

    except Exception as e:
        # If the centralized service didn't handle the error, handle it here
        if not hasattr(e, '__cause__') or 'ConversionService' not in str(type(e.__cause__)):
            end_time = time.time()
            duration_seconds = int(end_time - start_time)
            error_message = str(e)

            error_info = {
                "job_id": job_id,
                "conversion_id": None,
                "celery_task_id": celery_task_id,
                "error": error_message,
                "input_path": input_s3_url,
                "duration_seconds": duration_seconds,
            }

            logger.error(f"Video conversion v2 failed - Job: {job_id}, Error: {error_message}")

            # Send error notification
            if webhook_url:
                send_error_notification.delay(error_info, webhook_url)

        raise
