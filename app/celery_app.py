"""Celery Application Configuration for Media Convert Service"""

from celery import Celery
from decouple import config

from app.utils.logger import get_logger

logger = get_logger(__name__)

# Environment variables
RABBITMQ_HOST = config("RABBITMQ_HOST", default="localhost")
RABBITMQ_PORT = config("RABBITMQ_PORT", default=5672, cast=int)
RABBITMQ_USER = config("RABBITMQ_USER", default="admin")
RABBITMQ_PASSWORD = config("RABBITMQ_PASSWORD", default="admin")
RABBITMQ_VHOST = config("RABBITMQ_VHOST", default="media_convert")

# Celery configuration
CELERY_BROKER_URL = f"pyamqp://{RABBITMQ_USER}:{RABBITMQ_PASSWORD}@{RABBITMQ_HOST}:{RABBITMQ_PORT}/{RABBITMQ_VHOST}"

# Initialize Celery app
celery_app = Celery(
    "media_convert",
    broker=CELERY_BROKER_URL,
    backend="rpc://",
    include=["app.tasks.microservice_conversion"],
)

# Basic configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    broker_connection_retry_on_startup=True,
)

# Queue routing with project prefix
celery_app.conf.task_routes = {
    "app.tasks.microservice_conversion.*": {"queue": "media-convert.requests"},
}
