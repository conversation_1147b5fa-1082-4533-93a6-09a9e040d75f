"""
Consumer for processing conversion requests from RabbitMQ
"""

import signal
import sys
from app.services.messaging_service import messaging_service
from app.tasks.conversion_handler import handle_conversion_request
from app.utils.logger import get_logger

logger = get_logger(__name__)


class RequestConsumer:
    """Consumer that listens for conversion requests and processes them"""
    
    def __init__(self):
        self.running = False
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
        messaging_service.close()
        sys.exit(0)
    
    def start(self):
        """Start consuming conversion requests"""
        logger.info("Starting conversion request consumer...")
        self.running = True
        
        try:
            # Start consuming messages
            messaging_service.consume_conversion_requests(handle_conversion_request)
            
        except KeyboardInterrupt:
            logger.info("Consumer interrupted by user")
        except Exception as e:
            logger.error(f"Consumer error: {e}")
            raise
        finally:
            messaging_service.close()
            logger.info("Consumer stopped")


def main():
    """Main entry point for the consumer"""
    consumer = RequestConsumer()
    consumer.start()


if __name__ == "__main__":
    main()
