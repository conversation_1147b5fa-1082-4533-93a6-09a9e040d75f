"""
Core conversion service - centralized logic for video conversion
"""

import time
import shutil
from typing import Optional, Dict, Any

from app.services.video_conversion_service import VideoConversionService
from app.utils.logger import get_logger
from app.utils.s3_client import S3Client
from app.utils.ffmpeg_wrapper import FFmpegWrapper

logger = get_logger(__name__)


class ConversionService:
    """Centralized service for video conversion logic"""
    
    @staticmethod
    def execute_conversion(
        conversion_id: str,
        input_s3_path: str,
        output_s3_prefix: str,
        celery_task_id: str,
        bitrates: Optional[list] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute video conversion with centralized logic.
        
        Args:
            conversion_id (str): Unique conversion identifier
            input_s3_path (str): S3 URL of the input video file
            output_s3_prefix (str): S3 prefix for output files
            celery_task_id (str): Celery task ID for tracking
            bitrates (list, optional): Custom bitrate configurations
            metadata (dict, optional): Additional metadata for database
            
        Returns:
            dict: Conversion result with status and details
        """
        temp_dir = None
        start_time = time.time()
        
        logger.info(
            f"Starting conversion - ID: {conversion_id}, Task: {celery_task_id}",
            extra={
                "conversion_id": conversion_id,
                "celery_task_id": celery_task_id,
                "input_s3_path": input_s3_path,
                "output_s3_prefix": output_s3_prefix
            }
        )
        
        # Prepare metadata for database
        db_metadata = {
            "celery_task_id": celery_task_id,
            "started_at": start_time,
            "output_prefix": output_s3_prefix,
        }
        if metadata:
            db_metadata.update(metadata)
        
        # Create conversion record in database
        try:
            conversion_record = VideoConversionService.create_conversion(
                job_id=conversion_id,
                input_path=input_s3_path,
                metadata=db_metadata,
            )
            
            # Update status to PROCESSING
            VideoConversionService.update_status(
                job_id=conversion_id,
                status="PROCESSING",
                metadata={"processing_started_at": start_time},
            )
            
        except Exception as e:
            logger.error(f"Failed to create conversion record: {e}")
            return {
                "status": "failed",
                "conversion_id": conversion_id,
                "task_id": celery_task_id,
                "error": f"Database error: {str(e)}",
                "duration_seconds": int(time.time() - start_time),
            }
        
        try:
            # Initialize clients
            s3_client = S3Client()
            ffmpeg = FFmpegWrapper()
            
            # Step 1: Download video from S3
            logger.info(f"Downloading video from S3: {input_s3_path}")
            temp_dir, input_file = s3_client.download_file(input_s3_path, conversion_id)
            
            # Step 2: Create output directory
            output_dir = f"{temp_dir}/output"
            
            # Step 3: Convert to DASH (thumbnails generated automatically)
            logger.info("Converting video to DASH with thumbnails")
            conversion_result = ffmpeg.convert_to_dash(
                input_file, output_dir, conversion_id, bitrates
            )
            
            # Step 4: Upload DASH files and thumbnails to S3
            logger.info(f"Uploading DASH files to S3: {output_s3_prefix}")
            upload_result = s3_client.upload_directory(output_dir, output_s3_prefix, conversion_id)
            
            # Calculate processing duration
            end_time = time.time()
            duration_seconds = int(end_time - start_time)
            
            # Prepare output path
            output_path = f"{output_s3_prefix}/manifest.mpd"
            
            # Update database with success
            VideoConversionService.update_status(
                job_id=conversion_id,
                status="COMPLETED",
                output_path=output_path,
                duration_seconds=duration_seconds,
                metadata={
                    "completed_at": end_time,
                    "conversion_result": conversion_result,
                    "upload_result": upload_result,
                },
            )
            
            result = {
                "status": "completed",
                "conversion_id": conversion_id,
                "task_id": celery_task_id,
                "output_path": output_path,
                "duration_seconds": duration_seconds,
                "conversion_details": conversion_result,
                "upload_details": upload_result,
            }
            
            logger.info(
                f"Conversion completed successfully: {conversion_id}",
                extra={
                    "conversion_id": conversion_id,
                    "duration_seconds": duration_seconds,
                    "output_path": output_path
                }
            )
            
            return result
            
        except Exception as e:
            # Calculate processing duration for failed conversion
            end_time = time.time()
            duration_seconds = int(end_time - start_time)
            
            error_message = str(e)
            logger.error(
                f"Conversion failed: {conversion_id} - {error_message}",
                extra={
                    "conversion_id": conversion_id,
                    "error": error_message,
                    "duration_seconds": duration_seconds
                }
            )
            
            # Update database with failure
            VideoConversionService.update_status(
                job_id=conversion_id,
                status="FAILED",
                duration_seconds=duration_seconds,
                error_message=error_message,
                metadata={"failed_at": end_time, "error_details": str(e)},
            )
            
            result = {
                "status": "failed",
                "conversion_id": conversion_id,
                "task_id": celery_task_id,
                "error": error_message,
                "duration_seconds": duration_seconds,
            }
            
            return result
            
        finally:
            # Cleanup temporary files
            if temp_dir:
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup temp directory: {cleanup_error}")
    

