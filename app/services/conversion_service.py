"""
Core conversion service - centralized logic for video conversion
"""

import os
import shutil
import tempfile
import time
from typing import Any, Dict, Optional

from app.repositories.media_conversion_repository import MediaConversionRepository
from app.database.models import MediaType, OutputFormat
from app.services.converters import MediaConverterFactory
from app.utils.ffmpeg_wrapper import FFmpegWrapper
from app.utils.logger import get_logger
from app.utils.s3_client import S3Client

logger = get_logger(__name__)


class ConversionService:
    """Centralized service for video conversion logic"""

    @staticmethod
    def execute_conversion(
        conversion_id: str,
        input_s3_path: str,
        output_s3_prefix: str,
        celery_task_id: str,
        media_type: MediaType = MediaType.VIDEO,
        output_format: OutputFormat = OutputFormat.DASH,
        bitrates: Optional[list] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Execute media conversion with centralized logic.

        Args:
            conversion_id (str): Unique conversion identifier
            input_s3_path (str): S3 URL of the input media file
            output_s3_prefix (str): S3 prefix for output files
            celery_task_id (str): Celery task ID for tracking
            media_type (MediaType): Type of media to convert
            output_format (OutputFormat): Target output format
            bitrates (list, optional): Custom bitrate configurations (for video)
            metadata (dict, optional): Additional metadata for database

        Returns:
            dict: Conversion result with status and details
        """
        temp_dir = None
        start_time = time.time()

        logger.info(
            f"Starting conversion - ID: {conversion_id}, Task: {celery_task_id}",
            extra={
                "conversion_id": conversion_id,
                "celery_task_id": celery_task_id,
                "input_s3_path": input_s3_path,
                "output_s3_prefix": output_s3_prefix,
            },
        )

        # Prepare metadata for database
        db_metadata = {
            "celery_task_id": celery_task_id,
            "started_at": start_time,
            "output_prefix": output_s3_prefix,
        }
        if metadata:
            db_metadata.update(metadata)

        # Create conversion record in database
        try:
            conversion_record = MediaConversionRepository.create_conversion(
                conversion_id=conversion_id,
                input_path=input_s3_path,
                media_type=media_type,
                output_format=output_format,
                metadata=db_metadata,
            )

            if conversion_record is None:
                # Conversion already exists, check its status
                existing = MediaConversionRepository.get_conversion(conversion_id)
                if existing:
                    logger.info(f"Conversion {conversion_id} already exists with status: {existing.status} - ignoring duplicate request")
                    return {
                        "status": "duplicate",
                        "conversion_id": conversion_id,
                        "task_id": celery_task_id,
                        "error": f"Conversion already exists with status: {existing.status}",
                        "existing_status": existing.status,
                        "processing_duration_seconds": 0,
                    }
                else:
                    logger.error(f"Failed to create conversion record: {conversion_id}")
                    return {
                        "status": "failed",
                        "conversion_id": conversion_id,
                        "task_id": celery_task_id,
                        "error": "Failed to create conversion record",
                        "processing_duration_seconds": 0,
                    }

            # Update status to PROCESSING (only if record was created successfully)
            MediaConversionRepository.update_status(
                conversion_id=conversion_id,
                status="PROCESSING",
                metadata={"processing_started_at": start_time},
            )

        except Exception as e:
            logger.error(f"Failed to create conversion record: {e}")
            return {
                "status": "failed",
                "conversion_id": conversion_id,
                "task_id": celery_task_id,
                "error": f"Database error: {str(e)}",
                "processing_duration_seconds": int(time.time() - start_time),
            }

        try:
            # Initialize clients and converter
            s3_client = S3Client()
            converter = MediaConverterFactory.get_converter(media_type)

            # Step 1: Create temporary directory and download media from S3
            temp_dir = tempfile.mkdtemp(prefix=f"conversion_{conversion_id}_")
            logger.debug(f"Created temporary directory: {temp_dir}")

            # Extract filename from S3 URL
            filename = os.path.basename(input_s3_path.split("/")[-1])
            if not filename:
                # Use appropriate extension based on media type
                ext = "mp4" if media_type == MediaType.VIDEO else "jpg"
                filename = f"input_media.{ext}"

            input_file = os.path.join(temp_dir, filename)

            logger.debug(f"Downloading {media_type.value} from S3: {input_s3_path}")
            download_result = s3_client.download_file(
                input_s3_path, input_file, celery_task_id
            )

            if download_result["status"] != "success":
                raise Exception(
                    f"S3 download failed: {download_result.get('error', 'Unknown error')}"
                )

            # Step 2: Create output directory
            output_dir = f"{temp_dir}/output"

            # Step 3: Convert media using appropriate converter
            logger.debug(f"Converting {media_type.value} to {output_format.value}")
            conversion_options = {}
            if media_type == MediaType.VIDEO and bitrates:
                conversion_options['bitrates'] = bitrates

            conversion_result = converter.convert(
                input_file, output_dir, conversion_id, output_format, conversion_options
            )

            # Step 4: Upload converted files to S3
            logger.debug(f"Uploading {output_format.value} files to S3: {output_s3_prefix}")
            upload_result = s3_client.upload_directory(
                output_dir, output_s3_prefix, conversion_id
            )

            # Calculate processing duration
            end_time = time.time()
            processing_duration_seconds = int(end_time - start_time)

            # Prepare output path based on format
            if output_format == OutputFormat.DASH:
                output_path = f"{output_s3_prefix}/manifest.mpd"
            elif output_format in [OutputFormat.WEBP, OutputFormat.AVIF, OutputFormat.JPEG, OutputFormat.PNG]:
                output_path = f"{output_s3_prefix}/{conversion_id}.{output_format.value}"
            else:
                output_path = f"{output_s3_prefix}/output.{output_format.value}"

            # Update database with success
            MediaConversionRepository.update_status(
                conversion_id=conversion_id,
                status="COMPLETED",
                output_path=output_path,
                processing_duration_seconds=processing_duration_seconds,
                metadata={
                    "completed_at": end_time,
                    "conversion_result": conversion_result,
                    "upload_result": upload_result,
                },
            )

            result = {
                "status": "completed",
                "conversion_id": conversion_id,
                "task_id": celery_task_id,
                "output_path": output_path,
                "processing_duration_seconds": processing_duration_seconds,
                "conversion_details": conversion_result,
                "upload_details": upload_result,
            }

            logger.info(
                f"Conversion completed successfully: {conversion_id}",
                extra={
                    "conversion_id": conversion_id,
                    "processing_duration_seconds": processing_duration_seconds,
                    "output_path": output_path,
                },
            )

            return result

        except Exception as e:
            # Calculate processing duration for failed conversion
            end_time = time.time()
            processing_duration_seconds = int(end_time - start_time)

            error_message = str(e)
            logger.error(
                f"Conversion failed: {conversion_id} - {error_message}",
                extra={
                    "conversion_id": conversion_id,
                    "error": error_message,
                    "processing_duration_seconds": processing_duration_seconds,
                },
            )

            # Update database with failure
            MediaConversionRepository.update_status(
                conversion_id=conversion_id,
                status="FAILED",
                processing_duration_seconds=processing_duration_seconds,
                error_message=error_message,
                metadata={"failed_at": end_time, "error_details": str(e)},
            )

            result = {
                "status": "failed",
                "conversion_id": conversion_id,
                "task_id": celery_task_id,
                "error": error_message,
                "processing_duration_seconds": processing_duration_seconds,
            }

            return result

        finally:
            # Cleanup temporary files
            if temp_dir:
                try:
                    shutil.rmtree(temp_dir)
                    logger.debug(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup temp directory: {cleanup_error}")
