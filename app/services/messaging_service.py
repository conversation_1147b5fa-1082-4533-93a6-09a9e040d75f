"""
Messaging service for microservice communication via RabbitMQ
"""

import json
import pika
from typing import Callable, Optional
from decouple import config

from app.models.messages import ConversionRequest, ConversionResponse
from app.utils.logger import get_logger

logger = get_logger(__name__)


class MessagingService:
    """Service for publishing and consuming messages via RabbitMQ"""
    
    def __init__(self):
        # RabbitMQ connection parameters
        self.rabbitmq_host = config("RABBITMQ_HOST", default="localhost")
        self.rabbitmq_port = config("RABBITMQ_PORT", default=5672, cast=int)
        self.rabbitmq_user = config("RABBITMQ_USER", default="admin")
        self.rabbitmq_password = config("RABBITMQ_PASSWORD", default="admin")
        self.rabbitmq_vhost = config("RABBITMQ_VHOST", default="media_convert")
        
        # Queue names
        self.request_queue = "media-convert.requests"
        self.response_queue = "media-convert.responses"
        
        self._connection = None
        self._channel = None
    
    def _get_connection(self):
        """Get or create RabbitMQ connection"""
        if self._connection is None or self._connection.is_closed:
            credentials = pika.PlainCredentials(self.rabbitmq_user, self.rabbitmq_password)
            parameters = pika.ConnectionParameters(
                host=self.rabbitmq_host,
                port=self.rabbitmq_port,
                virtual_host=self.rabbitmq_vhost,
                credentials=credentials,
                heartbeat=600,
                blocked_connection_timeout=300
            )
            self._connection = pika.BlockingConnection(parameters)
        return self._connection
    
    def _get_channel(self):
        """Get or create RabbitMQ channel"""
        if self._channel is None or self._channel.is_closed:
            connection = self._get_connection()
            self._channel = connection.channel()
            
            # Declare queues to ensure they exist
            self._declare_queues()
            
        return self._channel
    
    def _declare_queues(self):
        """Declare the required queues"""
        if self._channel:
            # Request queue
            self._channel.queue_declare(
                queue=self.request_queue,
                durable=True,
                arguments={
                    "x-message-ttl": 3600000,  # 1 hour TTL
                    "x-max-length": 1000
                }
            )
            
            # Response queue
            self._channel.queue_declare(
                queue=self.response_queue,
                durable=True,
                arguments={
                    "x-message-ttl": 1800000,  # 30 minutes TTL
                    "x-max-length": 5000
                }
            )
            
            logger.info("Messaging queues declared successfully")
    
    def publish_conversion_request(self, request: ConversionRequest) -> bool:
        """
        Publish a conversion request to the request queue
        
        Args:
            request: ConversionRequest object
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            channel = self._get_channel()
            
            message_body = json.dumps(request.to_dict())
            
            channel.basic_publish(
                exchange="",
                routing_key=self.request_queue,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # Make message persistent
                    content_type="application/json",
                    message_id=request.conversion_id
                )
            )
            
            logger.info(
                f"Published conversion request: {request.conversion_id}",
                extra={"conversion_id": request.conversion_id, "queue": self.request_queue}
            )
            return True
            
        except Exception as e:
            logger.error(
                f"Failed to publish conversion request: {e}",
                extra={"conversion_id": request.conversion_id, "error": str(e)}
            )
            return False
    
    def publish_conversion_response(self, response: ConversionResponse, reply_to_queue: str) -> bool:
        """
        Publish a conversion response to the specified reply queue
        
        Args:
            response: ConversionResponse object
            reply_to_queue: Queue name to send the response to
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            channel = self._get_channel()
            
            # Declare the reply queue in case it doesn't exist
            channel.queue_declare(queue=reply_to_queue, durable=True)
            
            message_body = json.dumps(response.to_dict())
            
            channel.basic_publish(
                exchange="",
                routing_key=reply_to_queue,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # Make message persistent
                    content_type="application/json",
                    message_id=response.conversion_id
                )
            )
            
            logger.info(
                f"Published conversion response: {response.conversion_id}",
                extra={
                    "conversion_id": response.conversion_id,
                    "status": response.status,
                    "reply_to_queue": reply_to_queue
                }
            )
            return True
            
        except Exception as e:
            logger.error(
                f"Failed to publish conversion response: {e}",
                extra={
                    "conversion_id": response.conversion_id,
                    "reply_to_queue": reply_to_queue,
                    "error": str(e)
                }
            )
            return False
    
    def consume_conversion_requests(self, callback: Callable[[ConversionRequest], None]):
        """
        Start consuming conversion requests from the request queue
        
        Args:
            callback: Function to call when a request is received
        """
        try:
            channel = self._get_channel()
            
            def message_handler(ch, method, properties, body):
                try:
                    # Parse message
                    message_data = json.loads(body.decode('utf-8'))
                    request = ConversionRequest.from_dict(message_data)
                    
                    logger.info(
                        f"Received conversion request: {request.conversion_id}",
                        extra={"conversion_id": request.conversion_id}
                    )
                    
                    # Call the callback function
                    callback(request)
                    
                    # Acknowledge the message
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                    
                except Exception as e:
                    logger.error(
                        f"Error processing conversion request: {e}",
                        extra={"error": str(e), "message_body": body.decode('utf-8')}
                    )
                    # Reject and don't requeue on processing error
                    ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            
            # Set up consumer
            channel.basic_qos(prefetch_count=1)  # Process one message at a time
            channel.basic_consume(
                queue=self.request_queue,
                on_message_callback=message_handler
            )
            
            logger.info(f"Started consuming from queue: {self.request_queue}")
            channel.start_consuming()
            
        except Exception as e:
            logger.error(f"Error in request consumer: {e}")
            raise
    
    def close(self):
        """Close the connection"""
        if self._channel and not self._channel.is_closed:
            self._channel.close()
        if self._connection and not self._connection.is_closed:
            self._connection.close()
        logger.info("Messaging service connection closed")


# Global instance
messaging_service = MessagingService()
