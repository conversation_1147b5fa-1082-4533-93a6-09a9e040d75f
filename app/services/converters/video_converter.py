"""Video converter implementation"""

import os
from typing import Dict, Any, Optional, List

from app.database.models import MediaType, OutputFormat
from app.utils.ffmpeg_wrapper import FFmpegWrapper
from app.utils.logger import get_logger
from .base import BaseMediaConverter

logger = get_logger(__name__)


class VideoConverter(BaseMediaConverter):
    """Video converter using FFmpeg"""
    
    def __init__(self):
        self.ffmpeg = FFmpegWrapper()
    
    @property
    def supported_media_type(self) -> MediaType:
        return MediaType.VIDEO
    
    @property
    def supported_output_formats(self) -> List[OutputFormat]:
        return [OutputFormat.DASH, OutputFormat.HLS]
    
    def validate_input(self, input_file: str) -> bool:
        """Validate if input file is a supported video format"""
        if not os.path.exists(input_file):
            return False
        
        # Check file extension
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv'}
        _, ext = os.path.splitext(input_file.lower())
        return ext in video_extensions
    
    def convert(
        self,
        input_file: str,
        output_dir: str,
        conversion_id: str,
        output_format: OutputFormat,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Convert video to specified format
        
        Args:
            input_file: Path to input video file
            output_dir: Directory for output files
            conversion_id: Unique conversion identifier
            output_format: Target output format (DASH or HLS)
            options: Additional conversion options (bitrates, etc.)
            
        Returns:
            Dictionary with conversion results
        """
        logger.info(f"Starting video conversion: {conversion_id}")
        
        if not self.validate_input(input_file):
            raise ValueError(f"Invalid input file: {input_file}")
        
        if output_format not in self.supported_output_formats:
            raise ValueError(f"Unsupported output format: {output_format}")
        
        # Extract options
        bitrates = options.get('bitrates') if options else None
        
        try:
            if output_format == OutputFormat.DASH:
                result = self.ffmpeg.convert_to_dash(
                    input_file, output_dir, conversion_id, bitrates
                )
            elif output_format == OutputFormat.HLS:
                # Future implementation for HLS
                raise NotImplementedError("HLS conversion not yet implemented")
            else:
                raise ValueError(f"Unsupported format: {output_format}")
            
            logger.info(f"Video conversion completed: {conversion_id}")
            return result
            
        except Exception as e:
            logger.error(f"Video conversion failed: {conversion_id} - {e}")
            raise
    
    def get_file_info(self, input_file: str) -> Dict[str, Any]:
        """Get video file information using FFmpeg"""
        base_info = super().get_file_info(input_file)
        
        try:
            video_info = self.ffmpeg.get_video_info(input_file)
            base_info.update(video_info)
        except Exception as e:
            logger.warning(f"Could not get video info for {input_file}: {e}")
        
        return base_info
