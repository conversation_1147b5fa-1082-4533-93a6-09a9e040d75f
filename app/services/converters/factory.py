"""Factory for creating media converters"""

from typing import Dict, Type

from app.database.models import MediaType, OutputFormat
from app.utils.logger import get_logger
from .base import BaseMediaConverter
from .video_converter import VideoConverter
from .image_converter import ImageConverter

logger = get_logger(__name__)


class MediaConverterFactory:
    """Factory for creating appropriate media converters"""
    
    # Registry of available converters
    _converters: Dict[MediaType, Type[BaseMediaConverter]] = {
        MediaType.VIDEO: VideoConverter,
        MediaType.IMAGE: ImageConverter,
        # MediaType.AUDIO: AudioConverter,  # Future implementation
    }
    
    @classmethod
    def get_converter(cls, media_type: MediaType) -> BaseMediaConverter:
        """
        Get appropriate converter for media type
        
        Args:
            media_type: Type of media to convert
            
        Returns:
            Converter instance
            
        Raises:
            ValueError: If media type is not supported
        """
        converter_class = cls._converters.get(media_type)
        
        if not converter_class:
            raise ValueError(f"No converter available for media type: {media_type}")
        
        logger.debug(f"Creating converter for media type: {media_type}")
        return converter_class()
    
    @classmethod
    def get_supported_media_types(cls) -> list[MediaType]:
        """Get list of supported media types"""
        return list(cls._converters.keys())
    
    @classmethod
    def is_format_supported(cls, media_type: MediaType, output_format: OutputFormat) -> bool:
        """
        Check if a specific output format is supported for a media type
        
        Args:
            media_type: Type of media
            output_format: Desired output format
            
        Returns:
            True if format is supported, False otherwise
        """
        try:
            converter = cls.get_converter(media_type)
            return output_format in converter.supported_output_formats
        except ValueError:
            return False
    
    @classmethod
    def detect_media_type_from_file(cls, file_path: str) -> MediaType:
        """
        Detect media type from file extension
        
        Args:
            file_path: Path to the file
            
        Returns:
            Detected media type
            
        Raises:
            ValueError: If media type cannot be detected
        """
        import os
        
        _, ext = os.path.splitext(file_path.lower())
        
        # Video extensions
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv', '.m4v'}
        if ext in video_extensions:
            return MediaType.VIDEO
        
        # Image extensions
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.avif'}
        if ext in image_extensions:
            return MediaType.IMAGE
        
        # Audio extensions (future)
        audio_extensions = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'}
        if ext in audio_extensions:
            return MediaType.AUDIO
        
        raise ValueError(f"Cannot detect media type for file: {file_path}")
    
    @classmethod
    def register_converter(cls, media_type: MediaType, converter_class: Type[BaseMediaConverter]):
        """
        Register a new converter for a media type
        
        Args:
            media_type: Media type to register
            converter_class: Converter class to register
        """
        cls._converters[media_type] = converter_class
        logger.info(f"Registered converter for media type: {media_type}")
