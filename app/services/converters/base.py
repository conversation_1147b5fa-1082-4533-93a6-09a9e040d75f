"""Base abstract class for media converters"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from app.database.models import MediaType, OutputFormat


class BaseMediaConverter(ABC):
    """Abstract base class for all media converters"""

    @property
    @abstractmethod
    def supported_media_type(self) -> MediaType:
        """Return the media type this converter supports"""
        pass

    @property
    @abstractmethod
    def supported_output_formats(self) -> List[OutputFormat]:
        """Return list of output formats this converter supports"""
        pass

    @abstractmethod
    def convert(
        self,
        input_file: str,
        output_dir: str,
        conversion_id: str,
        output_format: OutputFormat,
        options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Convert media file to specified format

        Args:
            input_file: Path to input file
            output_dir: Directory for output files
            conversion_id: Unique conversion identifier
            output_format: Target output format
            options: Additional conversion options

        Returns:
            Dictionary with conversion results
        """
        pass

    @abstractmethod
    def validate_input(self, input_file: str) -> bool:
        """
        Validate if input file is supported by this converter

        Args:
            input_file: Path to input file

        Returns:
            True if file is supported, False otherwise
        """
        pass

    def get_file_info(self, input_file: str) -> Dict[str, Any]:
        """
        Get information about the input file

        Args:
            input_file: Path to input file

        Returns:
            Dictionary with file information
        """
        return {"file_path": input_file, "media_type": self.supported_media_type.value}
