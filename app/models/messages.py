"""
Message models for microservice communication
"""

from dataclasses import dataclass
from typing import Optional
import uuid


@dataclass
class ConversionRequest:
    """Message payload for video conversion requests"""
    
    conversion_id: str
    input_s3_path: str
    output_s3_prefix: str
    reply_to_queue: str
    
    @classmethod
    def create(
        cls,
        input_s3_path: str,
        output_s3_prefix: str,
        reply_to_queue: str,
        conversion_id: Optional[str] = None
    ) -> "ConversionRequest":
        """Create a new conversion request with auto-generated ID if not provided"""
        if conversion_id is None:
            conversion_id = str(uuid.uuid4())
            
        return cls(
            conversion_id=conversion_id,
            input_s3_path=input_s3_path,
            output_s3_prefix=output_s3_prefix,
            reply_to_queue=reply_to_queue
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            "conversion_id": self.conversion_id,
            "input_s3_path": self.input_s3_path,
            "output_s3_prefix": self.output_s3_prefix,
            "reply_to_queue": self.reply_to_queue
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "ConversionRequest":
        """Create from dictionary (JSON deserialization)"""
        return cls(
            conversion_id=data["conversion_id"],
            input_s3_path=data["input_s3_path"],
            output_s3_prefix=data["output_s3_prefix"],
            reply_to_queue=data["reply_to_queue"]
        )


@dataclass
class ConversionResponse:
    """Message payload for video conversion responses"""
    
    conversion_id: str
    status: str  # 'COMPLETED' or 'FAILED'
    output_s3_url: Optional[str] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            "conversion_id": self.conversion_id,
            "status": self.status,
            "output_s3_url": self.output_s3_url,
            "error_message": self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "ConversionResponse":
        """Create from dictionary (JSON deserialization)"""
        return cls(
            conversion_id=data["conversion_id"],
            status=data["status"],
            output_s3_url=data.get("output_s3_url"),
            error_message=data.get("error_message")
        )
    
    @classmethod
    def success(cls, conversion_id: str, output_s3_url: str) -> "ConversionResponse":
        """Create a successful response"""
        return cls(
            conversion_id=conversion_id,
            status="COMPLETED",
            output_s3_url=output_s3_url,
            error_message=None
        )
    
    @classmethod
    def failure(cls, conversion_id: str, error_message: str) -> "ConversionResponse":
        """Create a failure response"""
        return cls(
            conversion_id=conversion_id,
            status="FAILED",
            output_s3_url=None,
            error_message=error_message
        )
