"""
Centralized configuration for Media Convert Service
"""

from decouple import config


class RabbitMQConfig:
    """RabbitMQ connection configuration"""

    HOST = config("RABBITMQ_HOST", default="localhost")
    PORT = config("RABBITMQ_PORT", default=5672, cast=int)
    USER = config("RABBITMQ_USER", default="admin")
    PASSWORD = config("RABBITMQ_PASSWORD", default="admin")
    VHOST = config("RABBITMQ_VHOST", default="media_convert")

    @classmethod
    def get_broker_url(cls) -> str:
        """Get complete broker URL"""
        return f"pyamqp://{cls.USER}:{cls.PASSWORD}@{cls.HOST}:{cls.PORT}/{cls.VHOST}"


class DatabaseConfig:
    """Database configuration"""

    URL = config(
        "DATABASE_URL",
        default="postgresql://postgres:postgres@localhost:5432/media_convert"
    )
    POOL_SIZE = config("DB_POOL_SIZE", default=10, cast=int)
    MAX_OVERFLOW = config("DB_MAX_OVERFLOW", default=20, cast=int)
    POOL_RECYCLE = config("DB_POOL_RECYCLE", default=3600, cast=int)
    ECHO = config("SQL_ECHO", default=False, cast=bool)


class CeleryConfig:
    """Celery configuration"""

    CONCURRENCY = config("CELERY_CONCURRENCY", default=2, cast=int)
    MAX_TASKS_PER_CHILD = config("CELERY_MAX_TASKS_PER_CHILD", default=1000, cast=int)
    LOGLEVEL = config("CELERY_LOGLEVEL", default="info")
    QUEUES = config("CELERY_QUEUES", default="media-convert.requests")


class AWSConfig:
    """AWS configuration"""

    REGION = config("AWS_REGION", default="us-east-1")
    ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID", default="")
    SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY", default="")
    S3_BUCKET = config("AWS_S3_BUCKET", default="media-convert-bucket")


class LoggingConfig:
    """Logging configuration"""

    LEVEL = config("LOG_LEVEL", default="INFO")
    FORCE_COLOR = config("FORCE_COLOR", default="1")


class AppConfig:
    """Application configuration"""

    ENVIRONMENT = config("ENVIRONMENT", default="development")
    DEBUG = config("DEBUG", default=False, cast=bool)

    # Webhook for notifications (optional)
    WEBHOOK_URL = config("WEBHOOK_URL", default="")

    # Upload optimization
    MAX_UPLOAD_WORKERS = config("MAX_UPLOAD_WORKERS", default=None)
