"""Database models for Media Convert Service"""

import uuid

from sqlalchemy import <PERSON>SO<PERSON>, Column, DateTime, Integer, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from .connection import Base


class VideoConversion(Base):
    """Model for video conversion tracking"""

    __tablename__ = "video_conversions"

    # Primary key - uses conversion_id from message payload
    id = Column(String(255), primary_key=True)

    # File paths
    input_path = Column(String(1000), nullable=False)
    output_path = Column(String(1000), nullable=True)

    # Status tracking
    status = Column(String(50), nullable=False, default="PENDING", index=True)
    # Status values: PENDING, PROCESSING, COMPLETED, FAILED

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Processing details
    duration_seconds = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)

    # Metadata (JSON field for flexible data storage)
    metadata_json = Column(JSON, nullable=True)

    def __repr__(self):
        return f"<VideoConversion(id={self.id}, status={self.status})>"

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "input_path": self.input_path,
            "output_path": self.output_path,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "duration_seconds": self.duration_seconds,
            "error_message": self.error_message,
            "metadata_json": self.metadata_json,
        }
