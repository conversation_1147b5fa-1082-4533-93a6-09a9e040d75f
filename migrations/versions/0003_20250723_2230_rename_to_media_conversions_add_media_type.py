"""Rename to media_conversions and add media_type and output_format

Revision ID: 0003
Revises: 0002
Create Date: 2025-07-23 22:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0003'
down_revision = '0002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Rename table from video_conversions to media_conversions
    op.rename_table('video_conversions', 'media_conversions')
    
    # Add new columns
    op.add_column('media_conversions', sa.Column('media_type', sa.String(length=50), nullable=False, server_default='video'))
    op.add_column('media_conversions', sa.Column('output_format', sa.String(length=50), nullable=True))
    
    # Create indexes for new columns
    op.create_index(op.f('ix_media_conversions_media_type'), 'media_conversions', ['media_type'], unique=False)
    op.create_index(op.f('ix_media_conversions_output_format'), 'media_conversions', ['output_format'], unique=False)
    
    # Remove server_default after adding the column
    op.alter_column('media_conversions', 'media_type', server_default=None)


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f('ix_media_conversions_output_format'), table_name='media_conversions')
    op.drop_index(op.f('ix_media_conversions_media_type'), table_name='media_conversions')
    
    # Drop new columns
    op.drop_column('media_conversions', 'output_format')
    op.drop_column('media_conversions', 'media_type')
    
    # Rename table back
    op.rename_table('media_conversions', 'video_conversions')
