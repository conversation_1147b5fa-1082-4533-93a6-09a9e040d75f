"""Create media_conversions table

Revision ID: 0001
Revises: 
Create Date: 2025-07-23 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create media_conversions table with final structure
    op.create_table('media_conversions',
        sa.Column('id', sa.String(length=255), nullable=False),
        sa.Column('media_type', sa.String(length=50), nullable=False),
        sa.Column('input_path', sa.String(length=1000), nullable=False),
        sa.Column('output_path', sa.String(length=1000), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('output_format', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('processing_duration_seconds', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('metadata_json', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_media_conversions_media_type'), 'media_conversions', ['media_type'], unique=False)
    op.create_index(op.f('ix_media_conversions_output_format'), 'media_conversions', ['output_format'], unique=False)
    op.create_index(op.f('ix_media_conversions_status'), 'media_conversions', ['status'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f('ix_media_conversions_status'), table_name='media_conversions')
    op.drop_index(op.f('ix_media_conversions_output_format'), table_name='media_conversions')
    op.drop_index(op.f('ix_media_conversions_media_type'), table_name='media_conversions')
    
    # Drop table
    op.drop_table('media_conversions')
