"""Remove job_id and use conversion_id as primary key

Revision ID: 0002
Revises: 0001
Create Date: 2025-07-23 22:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0002'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop the existing table and recreate with new structure
    # This is safe for development, but in production you'd want to migrate data
    
    # Drop indexes first
    op.drop_index('ix_video_conversions_job_id', table_name='video_conversions')
    op.drop_index('ix_video_conversions_status', table_name='video_conversions')
    
    # Drop table
    op.drop_table('video_conversions')
    
    # Recreate table with new structure
    op.create_table('video_conversions',
        sa.Column('id', sa.String(length=255), nullable=False),
        sa.Column('input_path', sa.String(length=1000), nullable=False),
        sa.Column('output_path', sa.String(length=1000), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('duration_seconds', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('metadata_json', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_video_conversions_status'), 'video_conversions', ['status'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f('ix_video_conversions_status'), table_name='video_conversions')
    
    # Drop table
    op.drop_table('video_conversions')
    
    # Recreate original table structure
    op.create_table('video_conversions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('job_id', sa.String(length=255), nullable=False),
        sa.Column('input_path', sa.String(length=1000), nullable=False),
        sa.Column('output_path', sa.String(length=1000), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('duration_seconds', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('metadata_json', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create original indexes
    op.create_index(op.f('ix_video_conversions_job_id'), 'video_conversions', ['job_id'], unique=True)
    op.create_index(op.f('ix_video_conversions_status'), 'video_conversions', ['status'], unique=False)
