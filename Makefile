# Media Convert Service - Makefile

.PHONY: help setup up down clean logs worker test lint format db-test db-migrate db-reset

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make setup    - Initial setup"
	@echo "  make up       - Start RabbitMQ"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make test     - Run tests"
	@echo "  make lint     - Run linting"
	@echo "  make format   - Format code"
	@echo "  make logs     - View logs"
	@echo "  make clean    - Clean up"
	@echo ""
	@echo "Database Commands:"
	@echo "  make db-test    - Test database connection"
	@echo "  make db-migrate - Run database migrations"
	@echo "  make db-reset   - Reset database (DANGER)"
	@echo ""
	@echo "Conversion Commands:"
	@echo "  make test-conversion - Test video conversion"
	@echo "  make list-conversions - List recent conversions"
	@echo "  make status JOB_ID=<id> - Check conversion status"

# Setup
setup:
	@mkdir -p logs temp
	@if [ ! -f .env ]; then cp .env.example .env; fi

# Services
up:
	@docker-compose up -d postgres rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

worker-poetry:
	@docker-compose --profile poetry up -d media-convert-worker-poetry

exec:
	@docker-compose exec -it media-convert-worker /bin/bash

down:
	@docker-compose --profile worker down -v

# Testing
test:
	@docker-compose exec media-convert-worker pytest -v

# Code quality
lint:
	@docker-compose exec media-convert-worker ruff check app/ --fix

format:
	@docker-compose exec media-convert-worker ruff format app/

# Logs
logs:
	@docker-compose logs -f media-convert-worker

# Cleanup
clean:
	@docker-compose --profile worker down -v
	@docker system prune -f

clean-cache:
	@echo "Cleaning Python cache files..."
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -type f -delete 2>/dev/null || true
	@find . -name "*.pyo" -type f -delete 2>/dev/null || true
	@echo "Cache cleaned!"

# Build
build:
	@docker-compose build media-convert-worker

build-poetry:
	@docker-compose build media-convert-worker-poetry

# Poetry commands
poetry-install:
	@poetry install

poetry-update:
	@poetry update

poetry-shell:
	@poetry shell

poetry-add:
	@echo "Usage: make poetry-add PACKAGE=package-name"
	@poetry add $(PACKAGE)

poetry-add-dev:
	@echo "Usage: make poetry-add-dev PACKAGE=package-name"
	@poetry add --group dev $(PACKAGE)

# Database commands
db-test:
	@docker-compose exec media-convert-worker python scripts/db-commands.py test

db-migrate:
	@docker-compose exec media-convert-worker /app/scripts/run-migrations.sh

db-reset:
	@echo "WARNING: This will DELETE ALL DATA in the database!"
	@read -p "Type 'YES' to confirm: " confirm && [ "$$confirm" = "YES" ] || exit 1
	@docker-compose exec media-convert-worker python scripts/db-commands.py drop
	@docker-compose exec media-convert-worker /app/scripts/run-migrations.sh


