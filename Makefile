# Media Convert Service - Makefile

.PHONY: help up worker down logs build test test-unit test-cov test-watch lint format clean install

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make up       - Start services (PostgreSQL, RabbitMQ)"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make logs     - View worker logs"
	@echo "  make build    - Build worker image"
	@echo ""
	@echo "Development Commands:"
	@echo "  make install  - Install dependencies with Poetry"
	@echo "  make test     - Run all tests"
	@echo "  make test-unit- Run only unit tests (validation + messages)"
	@echo "  make test-cov - Run tests with coverage report"
	@echo "  make test-watch - Run tests in watch mode"
	@echo "  make lint     - Run linting"
	@echo "  make format   - Format code"
	@echo "  make clean    - Complete project reset (containers, cache, temp files)"

# Services
up:
	@mkdir -p logs temp
	@docker-compose up -d postgres rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

down:
	@docker-compose --profile worker down -v

# Logs
logs:
	@docker-compose logs -f media-convert-worker

# Build
build:
	@docker-compose build media-convert-worker

# Development
install:
	@echo "Installing dependencies with Poetry..."
	@poetry install
	@echo "Dependencies installed successfully!"

test:
	@echo "Running all tests..."
	@poetry run pytest tests/ -v --tb=short
	@echo "Tests completed!"

test-unit:
	@echo "Running unit tests (validation + messages)..."
	@poetry run pytest tests/test_validation.py tests/test_messages.py -v --tb=short
	@echo "Unit tests completed!"

test-cov:
	@echo "Running tests with coverage..."
	@poetry run python -m pytest tests/ -v --cov=app --cov-report=term-missing --cov-report=html
	@echo "Coverage report generated in htmlcov/"

test-watch:
	@echo "Running tests in watch mode (Ctrl+C to stop)..."
	@poetry run pytest tests/ -v --tb=short -f

lint:
	@echo "Running linting..."
	@poetry run ruff check app/ --fix
	@echo "Linting completed!"

format:
	@echo "Formatting code..."
	@poetry run ruff format app/
	@echo "Code formatted!"

# Complete cleanup - reset everything
clean:
	@echo "Complete project cleanup..."

	@echo "Stopping containers..."
	@docker-compose --profile worker down -v

	@echo "Cleaning Python bytecode cache..."
	@find $(CURDIR) -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find $(CURDIR) -type f -name "*.py[co]" -exec rm -f {} + 2>/dev/null || true

	@echo "Cleaning test and coverage files..."
	@rm -rf $(CURDIR)/.pytest_cache $(CURDIR)/.coverage $(CURDIR)/htmlcov 2>/dev/null || true

	@echo "Cleaning linting cache..."
	@rm -rf $(CURDIR)/.ruff_cache 2>/dev/null || true

	@echo "Cleaning temporary files and directories..."
	@rm -rf $(CURDIR)/logs $(CURDIR)/temp 2>/dev/null || true

	@echo "Cleaning Poetry cache..."
	@poetry cache clear pypi --all || true

	@echo "Removing Poetry virtual environments..."
	@poetry env list --full-path | xargs -r rm -rf

	@echo "Project completely cleaned!"
