# Media Convert Service - Makefile

.PHONY: help up worker down logs build test lint format clean

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make up       - Start services (PostgreSQL, RabbitMQ)"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make logs     - View worker logs"
	@echo "  make build    - Build worker image"
	@echo ""
	@echo "Development Commands:"
	@echo "  make test     - Run tests"
	@echo "  make lint     - Run linting"
	@echo "  make format   - Format code"
	@echo "  make clean    - Complete project reset (containers, cache, temp files)"

# Services
up:
	@mkdir -p logs temp
	@docker-compose up -d postgres rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

down:
	@docker-compose --profile worker down -v

# Logs
logs:
	@docker-compose logs -f media-convert-worker

# Build
build:
	@docker-compose build media-convert-worker

# Development
test:
	@docker-compose exec media-convert-worker poetry run pytest -v

lint:
	@docker-compose exec media-convert-worker poetry run ruff check app/ --fix

format:
	@docker-compose exec media-convert-worker poetry run ruff format app/

# Complete cleanup - reset everything
clean:
	@echo "🧹 Complete project cleanup..."
	@echo "Stopping all containers..."
	@docker-compose --profile worker down -v
	@echo "Removing Docker images..."
	@docker system prune -af
	@echo "Cleaning Python cache..."
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.pyc" -type f -delete 2>/dev/null || true
	@find . -name "*.pyo" -type f -delete 2>/dev/null || true
	@find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name ".ruff_cache" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "Cleaning temporary files..."
	@rm -rf logs/* temp/* 2>/dev/null || true
	@echo "Cleaning Poetry cache..."
	@poetry cache clear --all pypi 2>/dev/null || true
	@echo "✅ Project completely cleaned!"