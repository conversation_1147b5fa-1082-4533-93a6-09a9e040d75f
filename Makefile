# Media Convert Service - Makefile

.PHONY: help up worker down logs build clean

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make up       - Start services (PostgreSQL, RabbitMQ)"
	@echo "  make worker   - Start worker"
	@echo "  make down     - Stop services"
	@echo "  make logs     - View worker logs"
	@echo "  make build    - Build worker image"
	@echo "  make clean    - Clean up containers and images"

# Services
up:
	@mkdir -p logs temp
	@docker-compose up -d postgres rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

down:
	@docker-compose --profile worker down -v

# Logs
logs:
	@docker-compose logs -f media-convert-worker

# Build
build:
	@docker-compose build media-convert-worker

# Cleanup
clean:
	@docker-compose --profile worker down -v
	@docker system prune -f