# Logging Configuration

O serviço de conversão de mídia utiliza o Loguru para logging estruturado e configurável por ambiente.

## Configuração por Ambiente

### Desenvolvimento
- **Formato**: Colorizado e legível para humanos
- **Nível padrão**: DEBUG
- **Saída**: Console apenas
- **Cores**: Habilitadas

### Produção
- **Formato**: Estruturado sem cores
- **Nível padrão**: INFO
- **Saída**: Console + arquivo
- **Arquivo**: Rotação automática e compressão

## Variáveis de Ambiente

### Básicas
```bash
LOG_LEVEL=INFO                    # DEBUG, INFO, WARNING, ERROR, CRITICAL
ENVIRONMENT=development           # development, production
FORCE_COLOR=1                     # 0=desabilitado, 1=habilitado
```

### Logging Estruturado
```bash
LOG_STRUCTURED=false              # true para formato estruturado
```

### Logging em Arquivo
```bash
LOG_FILE_ENABLED=false            # true para habilitar arquivo
LOG_FILE_PATH=/var/log/celery/media-convert.log
LOG_FILE_ROTATION=100 MB          # Tamanho para rotação
LOG_FILE_RETENTION=30 days        # Tempo de retenção
```

## Níveis de Log por Componente

### INFO (Produção)
- Início e fim de conversões
- Conexões de banco e RabbitMQ
- Erros e falhas
- Métricas importantes

### DEBUG (Desenvolvimento)
- Detalhes de inicialização
- Informações de sistema
- Caminhos de arquivos temporários
- Detalhes de FFmpeg

## Exemplos de Uso

### Desenvolvimento
```bash
# docker-compose.yml
environment:
  - LOG_LEVEL=DEBUG
  - ENVIRONMENT=development
  - LOG_STRUCTURED=false
  - LOG_FILE_ENABLED=false
```

### Produção
```bash
# docker-compose.prod.yml
environment:
  - LOG_LEVEL=INFO
  - ENVIRONMENT=production
  - LOG_STRUCTURED=true
  - LOG_FILE_ENABLED=true
  - FORCE_COLOR=0
```

## Estrutura dos Logs

### Formato de Desenvolvimento
```
2025-07-22 18:08:48.985 | INFO     | app.services.conversion_service:convert_video:49 | Starting conversion - ID: conv_123
```

### Formato de Produção
```
2025-07-22 18:08:48.985 | INFO     | app.services.conversion_service:convert_video:49 | Starting conversion - ID: conv_123
```

## Monitoramento

### Arquivos de Log
- **Localização**: `/var/log/celery/media-convert.log`
- **Rotação**: 100MB por arquivo
- **Retenção**: 30 dias
- **Compressão**: Gzip automático

### Métricas Importantes
- Duração de conversões
- Uso de recursos do sistema
- Erros de FFmpeg
- Falhas de upload/download S3

## Troubleshooting

### Logs Muito Verbosos
```bash
# Reduzir nível para WARNING
LOG_LEVEL=WARNING
```

### Sem Cores no Terminal
```bash
# Forçar cores
FORCE_COLOR=1
```

### Problemas de Permissão
```bash
# Verificar permissões do diretório de logs
sudo chown -R celery:celery /var/log/celery
```
