#!/usr/bin/env python3
"""
Test script for refactored tasks using centralized conversion service
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.messages import ConversionRequest
from app.tasks.microservice_conversion import handle_conversion_request_from_dict
from app.tasks.video_conversion import convert_video_to_dash_v2
from app.utils.logger import get_logger

logger = get_logger(__name__)


def test_microservice_task():
    """Test the refactored microservice task"""
    print("🧪 Testing Refactored Microservice Task")
    print("-" * 50)
    
    # Create test request
    request = ConversionRequest.create(
        input_s3_path="s3://test-bucket/microservice-test.mp4",
        output_s3_prefix="s3://test-bucket/dash/microservice-test/",
        reply_to_queue="test.responses"
    )
    
    print(f"📋 Conversion ID: {request.conversion_id}")
    print(f"📥 Input: {request.input_s3_path}")
    print(f"📤 Output: {request.output_s3_prefix}")
    print(f"📬 Reply to: {request.reply_to_queue}")
    
    try:
        # Submit the task
        task = handle_conversion_request_from_dict.delay(request.to_dict())
        
        print(f"✅ Microservice task submitted successfully!")
        print(f"🆔 Task ID: {task.id}")
        print(f"📊 Task State: {task.state}")
        
        return True
        
    except Exception as e:
        print(f"❌ Microservice task submission failed: {e}")
        return False


def test_direct_task_v2():
    """Test the refactored direct task (v2)"""
    print("\n🧪 Testing Refactored Direct Task (v2)")
    print("-" * 50)
    
    input_s3_url = "s3://test-bucket/direct-test-v2.mp4"
    output_s3_prefix = "s3://test-bucket/dash/direct-test-v2/"
    
    print(f"📥 Input: {input_s3_url}")
    print(f"📤 Output: {output_s3_prefix}")
    
    try:
        # Submit the task
        task = convert_video_to_dash_v2.delay(
            input_s3_url=input_s3_url,
            output_s3_prefix=output_s3_prefix
        )
        
        print(f"✅ Direct task v2 submitted successfully!")
        print(f"🆔 Task ID: {task.id}")
        print(f"📊 Task State: {task.state}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct task v2 submission failed: {e}")
        return False


def test_legacy_task():
    """Test the legacy direct task"""
    print("\n🧪 Testing Legacy Direct Task")
    print("-" * 50)
    
    from app.tasks.video_conversion import convert_video_to_dash
    
    input_s3_url = "s3://test-bucket/legacy-test.mp4"
    output_s3_prefix = "s3://test-bucket/dash/legacy-test/"
    
    print(f"📥 Input: {input_s3_url}")
    print(f"📤 Output: {output_s3_prefix}")
    
    try:
        # Submit the task
        task = convert_video_to_dash.delay(
            input_s3_url=input_s3_url,
            output_s3_prefix=output_s3_prefix
        )
        
        print(f"✅ Legacy task submitted successfully!")
        print(f"🆔 Task ID: {task.id}")
        print(f"📊 Task State: {task.state}")
        
        return True
        
    except Exception as e:
        print(f"❌ Legacy task submission failed: {e}")
        return False


def show_refactoring_summary():
    """Show summary of refactoring changes"""
    print("\n📋 Refactoring Summary")
    print("=" * 60)
    
    print("🔧 Changes Made:")
    print("1. ✅ Created ConversionService - centralized conversion logic")
    print("2. ✅ Refactored microservice tasks to use ConversionService")
    print("3. ✅ Created convert_video_to_dash_v2 - new direct task using ConversionService")
    print("4. ✅ Kept convert_video_to_dash - legacy task for backward compatibility")
    print("5. ✅ Eliminated code duplication between tasks")
    
    print("\n📚 Available Tasks:")
    print("• handle_conversion_request_from_dict - Microservice entry point")
    print("• process_conversion_request - Microservice conversion processor")
    print("• convert_video_to_dash_v2 - New direct task (recommended)")
    print("• convert_video_to_dash - Legacy direct task (backward compatibility)")
    print("• send_completion_notification - Webhook notifications")
    print("• send_error_notification - Error notifications")
    
    print("\n🎯 Benefits:")
    print("• ✅ Single source of truth for conversion logic")
    print("• ✅ Consistent behavior across all interfaces")
    print("• ✅ Easier maintenance and testing")
    print("• ✅ Backward compatibility maintained")
    print("• ✅ Flexible architecture for future enhancements")


def main():
    """Run all tests"""
    print("🎬 Refactored Tasks Test Suite")
    print("=" * 60)
    
    tests = [
        ("Microservice Task", test_microservice_task),
        ("Direct Task v2", test_direct_task_v2),
        ("Legacy Task", test_legacy_task),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Show refactoring summary
    show_refactoring_summary()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Refactoring is working correctly.")
        print("\n💡 Next steps:")
        print("   • Test with real S3 files")
        print("   • Monitor performance and behavior")
        print("   • Consider deprecating legacy task in future")
        return 0
    else:
        print("⚠️  Some tests failed. Check the configuration.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
        sys.exit(1)
