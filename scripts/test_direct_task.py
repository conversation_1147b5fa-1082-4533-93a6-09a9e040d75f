#!/usr/bin/env python3
"""
Test script to call the microservice task directly
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.messages import ConversionRequest
from app.tasks.microservice_conversion import handle_conversion_request_from_dict
from app.utils.logger import get_logger

logger = get_logger(__name__)


def test_direct_task_call():
    """Test calling the task directly"""
    print("🧪 Testing Direct Task Call")
    print("-" * 40)
    
    # Create test request
    request = ConversionRequest.create(
        input_s3_path="s3://test-bucket/sample.mp4",
        output_s3_prefix="s3://test-bucket/dash/test/",
        reply_to_queue="test.responses"
    )
    
    print(f"📋 Conversion ID: {request.conversion_id}")
    print(f"📥 Input: {request.input_s3_path}")
    print(f"📤 Output: {request.output_s3_prefix}")
    print(f"📬 Reply to: {request.reply_to_queue}")
    
    try:
        # Call the task directly (synchronously)
        result = handle_conversion_request_from_dict(request.to_dict())
        
        print(f"✅ Task completed successfully!")
        print(f"📊 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Task failed: {e}")
        return False


def main():
    """Run test"""
    print("🎬 Direct Task Test")
    print("=" * 30)
    
    success = test_direct_task_call()
    
    if success:
        print("\n✨ Test completed successfully!")
    else:
        print("\n❌ Test failed!")


if __name__ == "__main__":
    main()
