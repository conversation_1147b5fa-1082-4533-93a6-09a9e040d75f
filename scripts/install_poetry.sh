#!/bin/bash

set -e

echo "Checking if Poetry is already installed..."

if command -v poetry &> /dev/null; then
    echo "Poetry is already installed at: $(which poetry)"
    poetry --version
    exit 0
fi

echo "Installing Poetry using the official installer..."
curl -sSL https://install.python-poetry.org | python3 -

# Add Poetry to PATH if necessary
if ! grep -q 'poetry/env/bin' ~/.bashrc; then
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    echo "PATH updated in ~/.bashrc"
fi

# For zsh users
if [ -f "$HOME/.zshrc" ] && ! grep -q 'poetry/env/bin' ~/.zshrc; then
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.zshrc
    echo "PATH updated in ~/.zshrc"
fi

echo "Poetry installation completed successfully."
echo "You may need to open a new terminal or run: source ~/.bashrc (or ~/.zshrc) to update the PATH"
