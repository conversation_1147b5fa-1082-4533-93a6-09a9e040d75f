import sys
import os
from celery import Celery

# Add app to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.config import RabbitMQConfig
from app.database.models import MediaType, OutputFormat

# Para execução local, usar localhost em vez do nome do container
class LocalRabbitMQConfig(RabbitMQConfig):
    HOST = "localhost"

broker_url = LocalRabbitMQConfig.get_broker_url()

# Celery Initilize
celery = Celery(broker=broker_url)

def send(
    conversion_id: str,
    input_s3_path: str,
    output_s3_prefix: str,
    reply_to_queue: str,
    media_type: MediaType = MediaType.VIDEO,
    output_format: OutputFormat = OutputFormat.DASH
):
    """
    Envia a task Celery com os dados fornecidos.

    Args:
        conversion_id: Unique conversion identifier
        input_s3_path: S3 path to input media file
        output_s3_prefix: S3 prefix for output files
        reply_to_queue: Queue for response messages
        media_type: Type of media (video, image, audio)
        output_format: Target output format (dash, webp, etc.)
    """
    payload = {
        "conversion_id": conversion_id,
        "input_s3_path": input_s3_path,
        "output_s3_prefix": output_s3_prefix,
        "reply_to_queue": reply_to_queue,
        "media_type": media_type.value,
        "output_format": output_format.value
    }

    result = celery.send_task(
        "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
        args=[payload],
        queue="media-convert.requests"
    )

    print(f"Task sent successfully:")
    print(f"  Task ID: {result.id}")
    print(f"  Conversion ID: {conversion_id}")
    print(f"  Media Type: {media_type.value}")
    print(f"  Output Format: {output_format.value}")
    return result


if __name__ == "__main__":
    # Example usage for video conversion (default)
    print("Example 1: Video to DASH conversion")
    send(
        conversion_id="video-test-001",
        input_s3_path="s3://test-bucket/input/sample.mp4",
        output_s3_prefix="s3://test-bucket/output/video-test-001",
        reply_to_queue="response.queue"
    )

    print("\nExample 2: Video to DASH conversion (explicit)")
    send(
        conversion_id="video-test-002",
        input_s3_path="s3://test-bucket/input/sample.mp4",
        output_s3_prefix="s3://test-bucket/output/video-test-002",
        reply_to_queue="response.queue",
        media_type=MediaType.VIDEO,
        output_format=OutputFormat.DASH
    )

    print("\nExample 3: Future image to WebP conversion")
    try:
        send(
            conversion_id="image-test-001",
            input_s3_path="s3://test-bucket/input/sample.jpg",
            output_s3_prefix="s3://test-bucket/output/image-test-001",
            reply_to_queue="response.queue",
            media_type=MediaType.IMAGE,
            output_format=OutputFormat.WEBP
        )
    except Exception as e:
        print(f"  Note: Image conversion not yet implemented - {e}")

    print("\nAvailable media types:", [t.value for t in MediaType])
    print("Available output formats:", [f.value for f in OutputFormat])