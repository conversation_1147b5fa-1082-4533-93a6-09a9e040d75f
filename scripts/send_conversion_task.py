import os
from celery import Celery

# Para execução local, usar localhost em vez do nome do container
RABBITMQ_HOST = "localhost"
RABBITMQ_PORT = os.environ.get("RABBITMQ_PORT", 5672)
RABBITMQ_USER = os.environ.get("RABBITMQ_USER", "admin")
RABBITMQ_PASSWORD = os.environ.get("RABBITMQ_PASSWORD", "admin")
RABBITMQ_VHOST = os.environ.get("RABBITMQ_VHOST", "media_convert")

broker_url = f"pyamqp://{RABBITMQ_USER}:{RABBITMQ_PASSWORD}@{RABBITMQ_HOST}:{RABBITMQ_PORT}/{RABBITMQ_VHOST}"

# Celery Initilize
celery = Celery(broker=broker_url)

def send(conversion_id: str, input_s3_path: str, output_s3_prefix: str, reply_to_queue: str):
    """
    Envia a task Celery com os dados fornecidos.
    """
    payload = {
        "conversion_id": conversion_id,
        "input_s3_path": input_s3_path,
        "output_s3_prefix": output_s3_prefix,
        "reply_to_queue": reply_to_queue
    }

    result = celery.send_task(
        "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
        args=[payload],
        queue="media-convert.requests"
    )

    print(f"[OK] Task enviada com ID: {result.id}")
    return result