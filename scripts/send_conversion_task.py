import sys
import os
from celery import Celery

# Add app to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.config import RabbitMQConfig

# Para execução local, usar localhost em vez do nome do container
class LocalRabbitMQConfig(RabbitMQConfig):
    HOST = "localhost"

broker_url = LocalRabbitMQConfig.get_broker_url()

# Celery Initilize
celery = Celery(broker=broker_url)

def send(conversion_id: str, input_s3_path: str, output_s3_prefix: str, reply_to_queue: str):
    """
    Envia a task Celery com os dados fornecidos.
    """
    payload = {
        "conversion_id": conversion_id,
        "input_s3_path": input_s3_path,
        "output_s3_prefix": output_s3_prefix,
        "reply_to_queue": reply_to_queue
    }

    result = celery.send_task(
        "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
        args=[payload],
        queue="media-convert.requests"
    )

    print(f"[OK] Task enviada com ID: {result.id}")
    return result