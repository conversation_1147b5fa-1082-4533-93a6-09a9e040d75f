#!/usr/bin/env python3
"""
Script to run tests for the media conversion service
"""

import sys
import os
import subprocess
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_tests():
    """Run all tests with pytest using Poetry"""
    print("Running Media Conversion Service Tests")
    print("=" * 50)

    # Change to project root directory
    os.chdir(project_root)

    # Run pytest with Poetry
    cmd = [
        "poetry", "run", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--color=yes",
        "--durations=10"
    ]

    try:
        result = subprocess.run(cmd, check=True)
        print("\nAll tests passed!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\nTests failed with exit code: {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print("Poetry not found. Please install Poetry:")
        print("   curl -sSL https://install.python-poetry.org | python3 -")
        print("   Or install dependencies: poetry install")
        return 1


def run_specific_test(test_path):
    """Run a specific test file or test function using Poetry"""
    print(f"Running specific test: {test_path}")
    print("=" * 50)

    os.chdir(project_root)

    cmd = [
        "poetry", "run", "pytest",
        test_path,
        "-v",
        "--tb=short",
        "--color=yes"
    ]

    try:
        result = subprocess.run(cmd, check=True)
        print(f"\nTest {test_path} passed!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\nTest {test_path} failed with exit code: {e.returncode}")
        return e.returncode


def main():
    """Main function"""
    if len(sys.argv) > 1:
        # Run specific test
        test_path = sys.argv[1]
        return run_specific_test(test_path)
    else:
        # Run all tests
        return run_tests()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
