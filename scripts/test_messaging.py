#!/usr/bin/env python3
"""
Test script for microservice messaging communication
"""

import sys
import os
import time
import json
from uuid import uuid4

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.messages import ConversionRequest, ConversionResponse
from app.services.messaging_service import messaging_service
from app.utils.logger import get_logger

logger = get_logger(__name__)


def test_publish_request():
    """Test publishing a conversion request"""
    print("\n=== Testing Conversion Request Publishing ===")
    
    # Create a test request
    request = ConversionRequest.create(
        input_s3_path="s3://test-bucket/videos/sample.mp4",
        output_s3_prefix="s3://test-bucket/dash/test-conversion/",
        reply_to_queue="media-convert.responses"
    )
    
    print(f"Created request: {request.conversion_id}")
    print(f"Request payload: {json.dumps(request.to_dict(), indent=2)}")
    
    # Publish the request
    success = messaging_service.publish_conversion_request(request)
    
    if success:
        print("✅ Request published successfully!")
    else:
        print("❌ Failed to publish request")
    
    return success


def test_publish_response():
    """Test publishing a conversion response"""
    print("\n=== Testing Conversion Response Publishing ===")
    
    # Create a test response (success)
    conversion_id = str(uuid4())
    response = ConversionResponse.success(
        conversion_id=conversion_id,
        output_s3_url="s3://test-bucket/dash/test-conversion/manifest.mpd"
    )
    
    print(f"Created success response: {conversion_id}")
    print(f"Response payload: {json.dumps(response.to_dict(), indent=2)}")
    
    # Publish the response
    success = messaging_service.publish_conversion_response(
        response, 
        "media-convert.responses"
    )
    
    if success:
        print("✅ Success response published!")
    else:
        print("❌ Failed to publish success response")
    
    # Create a test response (failure)
    conversion_id = str(uuid4())
    response = ConversionResponse.failure(
        conversion_id=conversion_id,
        error_message="Test error: File not found"
    )
    
    print(f"\nCreated failure response: {conversion_id}")
    print(f"Response payload: {json.dumps(response.to_dict(), indent=2)}")
    
    # Publish the response
    success2 = messaging_service.publish_conversion_response(
        response, 
        "media-convert.responses"
    )
    
    if success2:
        print("✅ Failure response published!")
    else:
        print("❌ Failed to publish failure response")
    
    return success and success2


def test_message_serialization():
    """Test message serialization/deserialization"""
    print("\n=== Testing Message Serialization ===")
    
    # Test ConversionRequest
    original_request = ConversionRequest.create(
        input_s3_path="s3://test-bucket/input.mp4",
        output_s3_prefix="s3://test-bucket/output/",
        reply_to_queue="test.queue"
    )
    
    # Serialize and deserialize
    request_dict = original_request.to_dict()
    restored_request = ConversionRequest.from_dict(request_dict)
    
    print(f"Original request: {original_request}")
    print(f"Restored request: {restored_request}")
    
    if original_request == restored_request:
        print("✅ ConversionRequest serialization works!")
    else:
        print("❌ ConversionRequest serialization failed")
        return False
    
    # Test ConversionResponse (success)
    original_response = ConversionResponse.success(
        conversion_id="test-id",
        output_s3_url="s3://test-bucket/output.mpd"
    )
    
    response_dict = original_response.to_dict()
    restored_response = ConversionResponse.from_dict(response_dict)
    
    print(f"Original response: {original_response}")
    print(f"Restored response: {restored_response}")
    
    if original_response == restored_response:
        print("✅ ConversionResponse serialization works!")
    else:
        print("❌ ConversionResponse serialization failed")
        return False
    
    return True


def test_queue_connection():
    """Test RabbitMQ connection and queue declaration"""
    print("\n=== Testing Queue Connection ===")
    
    try:
        # This will test connection and queue declaration
        channel = messaging_service._get_channel()
        
        if channel and not channel.is_closed:
            print("✅ Successfully connected to RabbitMQ!")
            print("✅ Queues declared successfully!")
            return True
        else:
            print("❌ Failed to get channel")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Messaging System Tests")
    print("=" * 50)
    
    tests = [
        ("Queue Connection", test_queue_connection),
        ("Message Serialization", test_message_serialization),
        ("Request Publishing", test_publish_request),
        ("Response Publishing", test_publish_response),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Messaging system is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the configuration.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
        sys.exit(1)
    finally:
        # Clean up connection
        try:
            messaging_service.close()
        except:
            pass
