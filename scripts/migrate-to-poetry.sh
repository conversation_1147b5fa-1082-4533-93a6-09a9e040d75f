#!/bin/bash

# Script para migrar de requirements.txt para Poetry
# Este script ajuda na transição do projeto para usar Poetry

set -e

echo "🚀 Migrando projeto para Poetry..."

# Verificar se Poetry está instalado
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry não está instalado. Instalando..."
    curl -sSL https://install.python-poetry.org | python3 -
    export PATH="$HOME/.local/bin:$PATH"
fi

echo "✅ Poetry encontrado: $(poetry --version)"

# Verificar se pyproject.toml existe
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml não encontrado!"
    exit 1
fi

echo "✅ pyproject.toml encontrado"

# Instalar dependências
echo "📦 Instalando dependências..."
poetry install

echo "✅ Dependências instaladas com sucesso!"

# Verificar se o ambiente virtual foi criado
echo "🔍 Verificando ambiente virtual..."
poetry env info

echo ""
echo "🎉 Migração para Poetry concluída!"
echo ""
echo "📋 Próximos passos:"
echo "  1. Use 'poetry shell' para ativar o ambiente virtual"
echo "  2. Use 'poetry add <package>' para adicionar novas dependências"
echo "  3. Use 'poetry add --group dev <package>' para dependências de desenvolvimento"
echo "  4. Use 'make worker' para executar o worker (agora usa Poetry automaticamente)"
echo ""
echo "📚 Comandos úteis:"
echo "  poetry install          - Instalar dependências"
echo "  poetry update           - Atualizar dependências"
echo "  poetry add <package>    - Adicionar dependência"
echo "  poetry remove <package> - Remover dependência"
echo "  poetry shell            - Ativar ambiente virtual"
echo "  poetry run <command>    - Executar comando no ambiente virtual"
echo ""
