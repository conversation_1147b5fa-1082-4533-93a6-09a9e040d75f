[tool.poetry]
name = "media-convert"
version = "1.0.0"
description = "Media Convert Service - Video processing microservice with FFmpeg and DASH conversion"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"
# Celery and message broker (kombu included with celery)
celery = "5.3.4"
# AWS SDK
boto3 = "1.34.0"
botocore = "1.34.0"
# HTTP client for health checks and notifications
requests = "2.31.0"
# Environment variable management
python-decouple = "3.8"
# Logging and monitoring
loguru = "0.7.2"
# JSON handling
orjson = "3.9.10"
# Validation and data models
pydantic = "2.5.2"
# Retry mechanisms
tenacity = "8.2.3"
# Date and time utilities
python-dateutil = "2.8.2"
# System monitoring for EKS
psutil = "5.9.6"
# Database dependencies
psycopg2-binary = "2.9.9"
sqlalchemy = "2.0.23"
alembic = "1.13.1"

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "7.4.4"
pytest-cov = "4.1.0"
pytest-mock = "3.14.0"
pytest-asyncio = "0.23.0"
# Code quality
ruff = "0.1.6"
black = "23.12.0"
isort = "5.13.0"
mypy = "1.8.0"
# Development tools
ipython = "8.18.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 88
target-version = "py312"
select = ["E", "W", "F", "I"]
ignore = ["E501"]

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = ["--cov=app", "--cov-report=term-missing"]
