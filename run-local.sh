#!/bin/bash

# Faz com que todas as variáveis declaradas sejam exportadas automaticamente
set -a

# Carrega o arquivo .env
source .env

# Desativa o comportamento de export automático
set +a

python -c "
from scripts.send_conversion_task import send

result = send(
    conversion_id="simple-call-001",
    input_s3_path="s3://keeps.kontent.media.hml/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/general/teste_8d01.mp4",
    output_s3_prefix="ss3://keeps.kontent.media.hml/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/mpdash/03b68878-a1ee-4302-9413-317e4ad43ae6",
    reply_to_queue="media-convert.responses"
)

print(f'Task ID: {result.id}')
"