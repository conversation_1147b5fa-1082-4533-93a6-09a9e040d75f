services:
  # PostgreSQL - Database
  postgres:
    image: postgres:15-alpine
    container_name: media-convert-postgres
    environment:
      POSTGRES_DB: media_convert_dev_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d media_convert_dev_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - media-convert-network

  # RabbitMQ - Broker and Result Backend
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: media-convert-rabbitmq
    hostname: rabbitmq
    ports:
      - "5672:5672"    # AMQP port
      - "15672:15672"  # Management UI
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
      RABBITMQ_DEFAULT_VHOST: media_convert
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - media-convert-network

  # Media Convert Worker
  media-convert-worker:
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
    container_name: media-convert-worker
    environment:
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=admin
      - RABBITMQ_PASSWORD=admin
      - RABBITMQ_VHOST=media_convert
      - CELERY_QUEUES=media-convert.requests
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-your-key}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-your-secret}
      - AWS_DEFAULT_REGION=${AWS_REGION:-us-east-1}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET:-media-convert-bucket}
      - CELERY_CONCURRENCY=2
      - LOG_LEVEL=DEBUG
      - ENVIRONMENT=development
      - DATABASE_URL=********************************************/media_convert_dev_db
    volumes:
      - .:/app
      - ./logs:/var/log/celery
      - ./temp:/tmp/media-convert
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgres:
        condition: service_healthy
    networks:
      - media-convert-network
    profiles:
      - worker



volumes:
  postgres_data:
  rabbitmq_data:

networks:
  media-convert-network:
    driver: bridge
