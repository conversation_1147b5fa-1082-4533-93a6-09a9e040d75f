# Media Convert Service Environment Variables

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin
RABBITMQ_VHOST=media_convert

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/media_convert_dev_db

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Celery Configuration
CELERY_CONCURRENCY=2

# Logging Configuration
LOG_LEVEL=INFO
LOG_STRUCTURED=false
LOG_FILE_ENABLED=false
LOG_FILE_PATH=/var/log/celery/media-convert.log
LOG_FILE_ROTATION=100 MB
LOG_FILE_RETENTION=30 days
FORCE_COLOR=1

# Environment
ENVIRONMENT=development

# Webhook for notifications (optional)
WEBHOOK_URL=https://your-webhook-url.com/notifications
