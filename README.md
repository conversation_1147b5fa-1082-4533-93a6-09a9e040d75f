# Media Convert Service

A high-performance video conversion service built with Python, Celery, and FFmpeg. Converts videos to MPEG-DASH format with multiple bitrates and generates thumbnails.

## Features

- **MPEG-DASH Conversion**: Convert videos to adaptive streaming format
- **Multiple Bitrates**: Automatic generation of multiple quality levels (720p, 480p, 360p, 240p)
- **Thumbnail Generation**: Create video thumbnails during conversion
- **S3 Integration**: Download from and upload to AWS S3
- **Celery Workers**: Distributed task processing with RabbitMQ
- **Database Persistence**: Track conversion jobs and status in PostgreSQL
- **Comprehensive Logging**: Detailed logging with structured output
- **Error Handling**: Robust error handling and recovery
- **Microservice Communication**: Standardized request-response pattern for microservice integration

## Quick Start

```bash
make up      # Start services (PostgreSQL, RabbitMQ)
make worker  # Start Celery worker
```

## Architecture

- **Python 3.12**: Core application with Celery
- **FFmpeg**: Video processing engine
- **RabbitMQ**: Message broker for task distribution (auto-configured)
- **PostgreSQL**: Database for job tracking and persistence
- **AWS S3**: File storage and delivery
- **Docker**: Containerization and orchestration

## Usage

### Usage

This service is designed exclusively for microservice communication using standardized request-response patterns.

### Microservice Communication

For communication between microservices, use the standardized request-response pattern:

```python
from app.models.messages import ConversionRequest

# Create conversion request
request = ConversionRequest.create(
    input_s3_path="s3://bucket/video.mp4",
    output_s3_prefix="s3://bucket/dash/video/",
    reply_to_queue="your-service.responses"
)

# Send request
celery_app.send_task(
    "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
    args=[request.to_dict()],
    queue="media-convert.requests"
)
```

📖 **See [MICROSERVICE_COMMUNICATION.md](MICROSERVICE_COMMUNICATION.md) for complete integration guide.**

## Development

### Docker Commands
```bash
make up            # Start services (PostgreSQL, RabbitMQ)
make worker        # Start worker
make logs          # View worker logs
make down          # Stop services
make build         # Build worker image
make clean         # Complete project reset
```

### Development Commands
```bash
make test          # Run tests (via Poetry)
make lint          # Run linting (via Poetry)
make format        # Format code (via Poetry)
```

## Configuration

Environment variables in `.env`:
- `DATABASE_URL` - PostgreSQL connection string
- `RABBITMQ_HOST/USER/PASSWORD` - RabbitMQ configuration
- `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY` - AWS credentials
- `S3_BUCKET` - Target S3 bucket
- `CELERY_QUEUES` - Queue names (default: media-convert.video-conversion,media-convert.notifications)

## Database Tracking

All conversion jobs are automatically tracked in PostgreSQL with:

- **Job ID**: Unique identifier (Celery task ID)
- **Status**: PENDING, PROCESSING, COMPLETED, FAILED
- **Input/Output paths**: S3 URLs for source and destination
- **Duration**: Processing time in seconds
- **Error messages**: Detailed error information for failed jobs
- **Metadata**: Additional conversion details and metrics

## Output Structure

```
s3://bucket/output-prefix/
├── manifest.mpd
├── input_0init.m4s             # 720p
├── input_1init.m4s             # 480p
├── input_2init.m4s             # 360p
├── input_3init.m4s             # 240p
├── input_4init.m4s             # Audio
└── input_thumbnail_*.jpg       # Thumbnails
```

## Monitoring

Check conversion status in database:
```sql
SELECT id, job_id, status, error_message, created_at
FROM video_conversions
ORDER BY created_at DESC;
```

## Development

### Dependencies
This project uses Poetry for dependency management:

```bash
# Install dependencies
poetry install

# Add new dependency
poetry add <package>

# Development dependencies
poetry add --group dev <package>
```

### Local Development
```bash
# Activate Poetry environment
poetry shell

# Run locally (without Docker)
poetry run python -m app.celery_app
```

## Production Deployment

For AWS EKS deployment:
1. Build production image
2. Configure AWS credentials and RDS PostgreSQL
3. Set up RabbitMQ cluster
4. Deploy worker pods with appropriate resource limits
5. Configure auto-scaling based on queue length