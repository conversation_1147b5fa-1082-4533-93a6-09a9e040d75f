# Media Convert Service

A high-performance video conversion service built with Python, Celery, and FFmpeg. Converts videos to MPEG-DASH format with multiple bitrates and generates thumbnails.

## Features

- **MPEG-DASH Conversion**: Convert videos to adaptive streaming format
- **Multiple Bitrates**: Automatic generation of multiple quality levels (720p, 480p, 360p, 240p)
- **Thumbnail Generation**: Create video thumbnails during conversion
- **S3 Integration**: Download from and upload to AWS S3
- **Celery Workers**: Distributed task processing with RabbitMQ
- **Database Persistence**: Track conversion jobs and status in PostgreSQL
- **Comprehensive Logging**: Detailed logging with structured output
- **Error Handling**: Robust error handling and recovery
- **Webhook Notifications**: Optional webhook notifications for completion/errors

## Quick Start

```bash
make up      # Start services (PostgreSQL, RabbitMQ)
make worker  # Start Celery worker
```

## Architecture

- **Python 3.12**: Core application with Celery
- **FFmpeg**: Video processing engine
- **RabbitMQ**: Message broker for task distribution
- **PostgreSQL**: Database for job tracking and persistence
- **AWS S3**: File storage and delivery
- **Docker**: Containerization and orchestration

## Usage

### Direct Task Submission

Submit a video conversion task directly:

```python
from app.tasks.video_conversion import convert_video_to_dash_v2

# Basic conversion (recommended - uses centralized service)
result = convert_video_to_dash_v2.delay(
    's3://your-bucket/input/video.mp4',
    's3://your-bucket/output/video_dash'
)

# With webhook notification
result = convert_video_to_dash_v2.delay(
    's3://your-bucket/input/video.mp4',
    's3://your-bucket/output/video_dash',
    webhook_url='https://your-webhook-endpoint.com/notifications'
)

# Legacy task (still available for backward compatibility)
from app.tasks.video_conversion import convert_video_to_dash
result = convert_video_to_dash.delay(
    's3://your-bucket/input/video.mp4',
    's3://your-bucket/output/video_dash'
)
```

### Microservice Communication

For communication between microservices, use the standardized request-response pattern:

```python
from app.models.messages import ConversionRequest

# Create conversion request
request = ConversionRequest.create(
    input_s3_path="s3://bucket/video.mp4",
    output_s3_prefix="s3://bucket/dash/video/",
    reply_to_queue="your-service.responses"
)

# Send request
celery_app.send_task(
    "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
    args=[request.to_dict()],
    queue="media-convert.requests"
)
```

📖 **See [MICROSERVICE_COMMUNICATION.md](MICROSERVICE_COMMUNICATION.md) for complete integration guide.**

## Development

```bash
make up            # Start services
make worker        # Start worker
make logs          # View logs
make down          # Stop services
make clean         # Clean up
make exec          # Access worker shell
make db-test       # Test database connection
make db-migrate    # Run migrations
```

## Configuration

Environment variables in `.env`:
- `DATABASE_URL` - PostgreSQL connection string
- `RABBITMQ_HOST/USER/PASSWORD` - RabbitMQ configuration
- `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY` - AWS credentials
- `S3_BUCKET` - Target S3 bucket
- `CELERY_QUEUES` - Queue names (default: media-convert.video-conversion,media-convert.notifications)

## Database Tracking

All conversion jobs are automatically tracked in PostgreSQL with:

- **Job ID**: Unique identifier (Celery task ID)
- **Status**: PENDING, PROCESSING, COMPLETED, FAILED
- **Input/Output paths**: S3 URLs for source and destination
- **Duration**: Processing time in seconds
- **Error messages**: Detailed error information for failed jobs
- **Metadata**: Additional conversion details and metrics

## Output Structure

```
s3://bucket/output-prefix/
├── manifest.mpd
├── input_0init.m4s             # 720p
├── input_1init.m4s             # 480p
├── input_2init.m4s             # 360p
├── input_3init.m4s             # 240p
├── input_4init.m4s             # Audio
└── input_thumbnail_*.jpg       # Thumbnails
```

## Monitoring

Check conversion status in database:
```sql
SELECT id, job_id, status, error_message, created_at
FROM video_conversions
ORDER BY created_at DESC;
```

## Production Deployment

For AWS EKS deployment:
1. Build production image
2. Configure AWS credentials and RDS PostgreSQL
3. Set up RabbitMQ cluster
4. Deploy worker pods with appropriate resource limits
5. Configure auto-scaling based on queue length